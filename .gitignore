# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/
.history/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
venv/
coverage
stacks/*/.webpack/
stacks/*/.esbuild/
stacks/*/.serverless
stacks/*/.pytest_cache/
stacks/*/coverage
stacks/*/venv/
stacks/*/tests/venv/
stacks/*/tests/venv/
stacks/*/aws_glue.egg-info
stacks/resources/*/.serverless
eslintrc.json
*/*/.serverless
stacks/**/aws_glue.egg-info
stacks/**/venv
venv
aws_glue.egg-info
.pytest_cache
.scannerwork
.coverage
coverage.xml
