name: Unit Test & Sonar-Scan
run-name:  ${{ github.event_name == 'workflow_dispatch' && '🧪Unit-Test & ♾️Sonar-Scan Branch Analysis' || github.event_name == 'pull_request' && github.base_ref == 'dev' && '🧪Unit-Test & ♾️Sonar-Scan PR Analysis' || '🧪Unit-Test & ♾️Sonar-Scan' }}
on:
  workflow_dispatch: # When manually triggered it runs Sonar Scan for current branch [Sonar Branch Analysis]
  pull_request: # Triggerd when a PR is raised to merge changes to "dev" branch [Sonar PR Analysis]
    branches:
      - dev # PR : "dev" <--- "*" [any source branch]

jobs:
  Run:
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/Sonar-Scan-Nodejs.yml@main"
    with:
      additional_commands: "npm install"
      skip_unit_test:  false
      sonar_project_key_prefix: ${{ vars.SONAR_PROJECT_KEY_PREFIX_UNKNOWN }}
    secrets:
      sonar_token: ${{ secrets.ENTERPRISE_SONAR_API_TOKEN }}
      gh_auth_ssh_private_key: ${{ secrets.SERVICE_AC_PVT_SSH_KEY}}
