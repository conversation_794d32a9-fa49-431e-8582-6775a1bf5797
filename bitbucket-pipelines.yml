image: node:14.19.3-alpine
definitions:
  caches:
    sonar-cache: .sonar

steps:
  - step: &utest
      name: Run Unit Test
      caches:
        - node
      script:
        - apk update
        - apk add python3 git openssh-client
        - npm install -g jest
        - npm install
        - npm run test
      artifacts:
        - coverage/**

  - step: &python-script-unittest
      name: Run Pytest
      caches:
        - docker
      script:
        - apk update
        - apk add --no-cache py-pip bash jq
        - pip install awscli
        - bash ./scripts/glue_unittest.bash intca1 ca-central-1 ./ ccm-stacks
      services:
        - docker
      size: 2x
      artifacts:
        - coverage.xml

  - step: &sonar
      name: Sonar Code Analysis
      caches:
        - sonar-cache
      image: sonarsource/sonar-scanner-cli
      script:
        - export SONAR_LOGIN=$SONAR_API_TOKEN
        - export SONAR_PROJECT_BASE_DIR=.
        - /opt/sonar-scanner/bin/sonar-scanner -Dsonar.login=$SONAR_API_TOKEN -Dsonar.projectKey=argo:$BITBUCKET_REPO_SLUG -Dsonar.projectName=$BITBUCKET_REPO_SLUG -Dsonar.projectVersion=$BITBUCKET_BUILD_NUMBER -Dsonar.host.url=$SONAR_HOST_URL
  
  - step: &veracode
      name: Veracode scan
      image: signiant/docker-jenkins-centos7-java8
      script:
        - yum -y install zip
        # zip up the js
        - zip -r ${BITBUCKET_REPO_SLUG}.zip *
        # get the veracode wrapper
        - wget -q -O veracode-wrapper.jar https://repo1.maven.org/maven2/com/veracode/vosp/api/wrappers/vosp-api-wrappers-java/${VERACODE_WRAPPER_VERSION}/vosp-api-wrappers-java-${VERACODE_WRAPPER_VERSION}.jar
        # upload and scan with veracode
        - java -jar veracode-wrapper.jar -vid ${TEAM_ANALYSISCENTER_ID} -vkey ${TEAM_ANALYSISCENTER_KEY} -action UploadAndScan -appname "${BITBUCKET_REPO_SLUG}" -createprofile true -autoscan true -filepath ./${BITBUCKET_REPO_SLUG}.zip -version "Job ${BITBUCKET_BUILD_NUMBER}"

pipelines:
  branches:
    main:
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to PRODCA1 ca-central-1
          deployment: PRODCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to PRODCA1"

    "hotfix/*":
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to CRTCA1 ca-central-1
          deployment: CRTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to CRTCA1"

    release:
      #- step: *veracode
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to CRTCA1 ca-central-1
          deployment: CRTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to CRTCA1"
      - step:
          name: Deploy to PREPRODCA1 ca-central-1
          deployment: PREPRODCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to PREPRODCA1"

    bat:
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to BATCA1 ca-central-1
          deployment: BATCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to BATCA1"

    dev:
      - step: *utest
      # - step: *python-script-unittest
      # - step: *sonar
      - step:
          name: Deploy to INTCA1 ca-central-1
          deployment: INTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to INTCA1"

    feature/*:
      - step: *utest
      # - step: *python-script-unittest
      # - step: *sonar
      - step:
          name: Deploy to INTCA1 ca-central-1
          deployment: INTCA1
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/agent_schedule_converter_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/schedule_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent-schedule-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent_schedule_converter/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/agent-adherence-converter-dlq-alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/resources/adherence_text_to_csv_dlq_alarm/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent-adherence-glue-catalog/
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/agent_adherence_converter/
            - echo "deployed to INTCA1"