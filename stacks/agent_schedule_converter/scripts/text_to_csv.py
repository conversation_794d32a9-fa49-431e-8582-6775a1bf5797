import json
import boto3
import pandas as pd
import io
from io import StringIO
import os
import datetime

REGION = os.environ['REGION_NAME']
ERROR_QUEUE_URL = os.environ['ERROR_QUEUE_URL']
JOB_NAME = os.environ['JOB_NAME']

s3c = boto3.client('s3', region_name=REGION)

def send_error_to_queue(ex):
    sqs = boto3.client("sqs", endpoint_url=f"https://sqs.{REGION}.amazonaws.com", region_name=REGION)
    time = datetime.datetime.now()
    response = sqs.send_message(
        QueueUrl=ERROR_QUEUE_URL,
        DelaySeconds=5,
        MessageAttributes={
            "Title": {
                "DataType": "String",
                "StringValue": "Error From Lambda " + JOB_NAME,
            },
            "Author": {"DataType": "String", "StringValue": "Lambda Job"},
            "Date": {"DataType": "String", "StringValue": time.strftime("%m/%d/%Y")},
        },
        MessageBody=(str(ex)),
    )
    return response

def handler(event, context):

    try:
        bucket_name = event['Records'][0]['s3']['bucket']['name']
        source_file_key = event['Records'][0]['s3']['object']['key']
        obj = s3c.get_object(Bucket=bucket_name, Key=source_file_key)
        emp_df = pd.read_csv(io.BytesIO(obj['Body'].read()), sep='|', skiprows=range(
            1, 2), encoding='unicode_escape', parse_dates=['#fields:excDate', 'schedDate'], on_bad_lines='skip')
        emp_df['#fields:excDate'] = pd.to_datetime(emp_df['#fields:excDate'], errors='coerce', utc=True).dt.strftime('%m%d%y')
        emp_df['schedDate'] = pd.to_datetime(emp_df['schedDate'], errors='coerce', utc=True).dt.strftime('%m%d%y')
        emp_df.rename(columns={"#fields:excDate": "excDate"}, inplace=True)

        filenamefull = source_file_key.rsplit('/', 1)[1]
        filename = filenamefull.rsplit('.', 1)[0]

        csv_buffer = StringIO()
        emp_df.to_csv(csv_buffer, index=False)
        destination_file_key = 'data-ingestion/ccm/agent_schedule/formatted/' + filename + '.csv'
        response = s3c.put_object(Body=csv_buffer.getvalue(),
                                Bucket=bucket_name,
                                Key=destination_file_key)

        return response

    except Exception as err:
        if str(err) == "No columns to parse from file":
            print(f"no data in file to process: {err}")
        else:
            response = send_error_to_queue(err)
            print(err)
            print(response)
            raise Exception(f"Exception Occurred in Agent Schedule Text To CSV Lambda: {err}")