import sys
import os
import logging
import datetime
import boto3
import json

from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from botocore.exceptions import ClientError
from pyspark.sql.functions import col, concat, lit, input_file_name, substring, when, instr
from pyspark.context import SparkContext
from pyspark.conf import SparkConf
# Import glue-utils
from glue_utils.core.glue_context_wrapper import GlueContextWrapper

import urllib.parse
import requests
import uuid

# SQL
import pymysql

pymysql.install_as_MySQLdb()
import MySQLdb

# Initialize GlueContextWrapper instead of standard context
glue_wrapper = GlueContextWrapper()

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "CCM_BATCH_INGESTION_BUCKET_NAME",
        "AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME",
        "REG<PERSON>",
        "ACCOUNT_ID",
        "ERROR_QUEUE",
        "RDS_DATABASE_NAME",
        "RDS_SECRET_MANAGE",
        "AAD_SECRET_MANAGE",
        "AAD_TENENT_ID",
        "AAD_SCOPE",
        "AAD_ENDPOINT"
    ],
)

JOB_NAME = args["JOB_NAME"]
JOB_RUN_ID = args["JOB_RUN_ID"]
CCM_BATCH_INGESTION_BUCKET_NAME = args["CCM_BATCH_INGESTION_BUCKET_NAME"]
AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME = args[
    "AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME"
]
REGION = args["REGION"]
ACCOUNT_ID = args["ACCOUNT_ID"]
ERROR_QUEUE = args["ERROR_QUEUE"]
RDS_DATABASE_NAME = args["RDS_DATABASE_NAME"]
RDS_SECRET_MANAGE = args["RDS_SECRET_MANAGE"]
AAD_SECRET_MANAGE = args["AAD_SECRET_MANAGE"]
AAD_TENENT_ID = args["AAD_TENENT_ID"]
AAD_SCOPE = args["AAD_SCOPE"]
AAD_ENDPOINT = args["AAD_ENDPOINT"]
JDBC_USERNAME = ""
JDBC_PASSWORD = ""
JDBC_URL = ""
AAD_CLIENT_ID = ''
AAD_CLIENT_SECRET = ''


def get_logging_level():
    env_val = os.environ.get("LOGLEVEL", "INFO")
    return logging.getLevelName(env_val)


def get_logger():
    logger = logging.getLogger(__name__)
    logger.setLevel(get_logging_level())

    formatter = logging.Formatter(
        fmt="[%(filename)s:%(lineno)s - %(funcName)10s() ] %(asctime)-15s %(message)s",
        datefmt="%Y-%m-%d:%H:%M:%S"
    )

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


def send_error_to_queue(ex):
    sqs = boto3.client("sqs", endpoint_url=f"https://sqs.{REGION}.amazonaws.com", region_name=REGION)
    time = datetime.datetime.now()
    queue_url = f"https://sqs.{REGION}.amazonaws.com/{ACCOUNT_ID}/{ERROR_QUEUE}"
    response = sqs.send_message(
        QueueUrl=queue_url,
        DelaySeconds=5,
        MessageAttributes={
            "Title": {
                "DataType": "String",
                "StringValue": "Error From Glue Job " + JOB_NAME,
            },
            "Author": {"DataType": "String", "StringValue": "Glue Job"},
            "Date": {"DataType": "String", "StringValue": time.strftime("%m/%d/%Y")},
        },
        MessageBody=(str(ex)),
    )
    return response


def get_job_attempt(job_id: str) -> str:
    attempt_count = "0"
    if "attempt" in job_id:
        attempt_count = job_id.split("attempt_")[-1]

    return attempt_count


def get_source_df():   
    s3_path = f"s3://{CCM_BATCH_INGESTION_BUCKET_NAME}/{AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME}/"
    return glue_wrapper.load_data(
        source_type="S3",
        connection_options={
            "path": s3_path,
            "format": "csv",
            "format_options":{
                "withHeader": True,
                "separator": ",",
                "encoding": "UTF-8",
                "mode": "FAILFAST"
            },
            "recursiveFileLookup": "true",
            "optimizePerformance": "true",
            "parallelism": "100",
            "transformation_ctx":"source_df"
        }
    )

def get_ccm_db_agent_df():
    # Define sample query for more efficient filtering
    JDBC_USERNAME, JDBC_PASSWORD, JDBC_URL = get_secret()
    sample_query = f"""
        SELECT a.EmployeeNumber 
        FROM CcmDataStoreV2.Agent a
        LEFT JOIN AgentSchedule s ON a.EmployeeNumber = s.AgentEmployeeNumber
        WHERE a.EmployeeNumber LIKE 'AC%'
        AND s.AgentEmployeeNumber IS NULL
    """
    return glue_wrapper.load_data(
        source_type="JDBC",
        query=sample_query,
        fetch_size=100000,
        connection_options={
            "url": JDBC_URL,
            "user": JDBC_USERNAME,
            "password": JDBC_PASSWORD,
            "dbtable": "Agent",
            "driver": "com.mysql.cj.jdbc.Driver",
            "disableUrlUpdate":"true", 
            "zeroDateTimeBehavior": "convertToNull"
        }
    )

def convert_date_string_to_timestamp(time, format):
    if time is None:
        return None
    datetime_str = str(time).zfill(6)
    date_time = datetime.datetime.strptime(datetime_str, format)
    return date_time

def setup_context():
    # Create a SparkConf object
    # Set the desired configuration properties
    spark = glue_wrapper.spark
    sc = spark.sparkContext
    glue_context = GlueContext(sc)

    spark = glue_context.spark_session
    spark.conf.set("spark.sql.execution.arrow.enabled", "true")
    spark.conf.set("spark.sql.shuffle.partitions", "400")  # 2x core count
    spark.conf.set("spark.sql.adaptive.enabled", "true")  # Auto-optimize

    return glue_context, spark

def close_connection_db(conn):
    with conn.cursor() as cur:
        cur.close()
    return

def read_data_collections_and_execute_db(data_collections, logger, conn):
    records = []
    scheduledat_dates_general = []
    scheduledate_dates_loyalty = []
    logger.info(f"Processing data collections records")
    for index, record in enumerate(data_collections):
        agent_schedule_record = {}
        try:
            agent_schedule_record["ScheduledAt"] = (
                convert_date_string_to_timestamp(str(record.scheddate),"%m%d%y")
                if record["scheddate"] and bool(record.scheddate) == True
                else None
            )
        except Exception as err:
            logger.error(f"Schedule - field scheddate value not proper skipping: {err}")
            agent_schedule_record["ScheduledAt"] = None

        agent_schedule_record["AgentDepartmentCode"] = (
            record.muid if record["muid"] and bool(record.muid) == True else None
        )
        agent_schedule_record["CustomerServiceManager"] = (
            record["customer service mgr"].strip()
            if record["customer service mgr"]
            and bool(record["customer service mgr"].strip()) == True
            else None
        )
        agent_schedule_record["AgentDepartmentName"] = (
            record.department.strip()
            if record["department"] and bool(record.department.strip()) == True
            else None
        )
        agent_schedule_record["AgentEmployeeNumber"] = (
            record.externalid.strip() if record["externalid"] and bool(record.externalid) == True else None
        )

        if agent_schedule_record["AgentEmployeeNumber"] is None or agent_schedule_record["AgentEmployeeNumber"] == '':
            continue

        agent_schedule_record["ScheduleExceptionCode"] = (
            record.exception.strip()
            if record["exception"] and bool(record.exception.strip()) == True
            else None
        )

        try:
            agent_schedule_record["ExceptionStartedAt"] = (
                convert_date_string_to_timestamp(str(record.excdate) + ' ' + str(record.start), "%m%d%y %H:%M")
                if record["start"] and bool(record.start) == True
                else None
            )
        except Exception as err:
            logger.error(f"Schedule - field excdate or start value not proper skipping: {err}")
            agent_schedule_record["ExceptionStartedAt"] = None

        try:
            agent_schedule_record["ExceptionEndedAt"] = (
                convert_date_string_to_timestamp(str(record.excdate) + ' ' + str(record.stop), "%m%d%y %H:%M")
                if record["stop"] and bool(record.stop) == True
                else None
            )
        except Exception as err:
            logger.error(f"Schedule - field excdate or stop value not proper skipping: {err}")
            agent_schedule_record["ExceptionEndedAt"] = None

        if agent_schedule_record["ScheduledAt"] is None or agent_schedule_record["ExceptionStartedAt"] is None or agent_schedule_record["ExceptionEndedAt"] is None:
            continue

        agent_schedule_record["LogonID"] = (
            record.logonid
            if record["logonid"] and bool(record.logonid) == True
            else None
        )

        #If the ExceptionEndedAt is less than the ExceptionStartedAt, add 1 day to the ExceptionEndedAt
        if agent_schedule_record["ExceptionEndedAt"] < agent_schedule_record["ExceptionStartedAt"]:
            agent_schedule_record["ExceptionEndedAt"] = agent_schedule_record["ExceptionEndedAt"] + datetime.timedelta(days=1)

        current_time = datetime.datetime.now()
        agent_schedule_record["RecordCreatedAt"] = current_time
        agent_schedule_record["RecordModifiedAt"] = current_time

        # set AgentTypeCode as per file e.g Loyalty or General
        filename = (
            record.filename if record["filename"] and bool(record.filename) == True else None
        )
        agent_type_code = 'A'
        if "LoyaltyAgentScheduleDetailFile" in filename:
            agent_type_code = 'L'
        agent_schedule_record["AgentTypeCode"] = agent_type_code

        agent_schedule_record["RecordExpiredAt"] = agent_schedule_record["ScheduledAt"] + datetime.timedelta(days=365)

        record_value = (
            agent_schedule_record["ScheduledAt"],
            agent_schedule_record["AgentDepartmentCode"],
            agent_schedule_record["CustomerServiceManager"],
            agent_schedule_record["AgentDepartmentName"],
            agent_schedule_record["AgentEmployeeNumber"],
            agent_schedule_record["ScheduleExceptionCode"],
            agent_schedule_record["ExceptionStartedAt"],
            agent_schedule_record["ExceptionEndedAt"],
            agent_schedule_record["LogonID"],
            agent_schedule_record["RecordCreatedAt"],
            agent_schedule_record["RecordModifiedAt"],
            agent_schedule_record["RecordExpiredAt"],
            agent_schedule_record["AgentTypeCode"],
        )
        records.append(record_value)

        if agent_type_code == 'L':
            scheduledat_date = (agent_schedule_record["ScheduledAt"])
            if scheduledat_date not in scheduledate_dates_loyalty:
                scheduledate_dates_loyalty.append(scheduledat_date)
        else:
            scheduledat_date = (agent_schedule_record["ScheduledAt"])
            if scheduledat_date not in scheduledat_dates_general:
                scheduledat_dates_general.append(scheduledat_date)
    logger.info(f"Found {len(records)} records to process")
    insert_qry = """
        INSERT IGNORE INTO AgentSchedule (
            ScheduledAt,
            AgentDepartmentCode,
            CustomerServiceManager,
            AgentDepartmentName,
            AgentEmployeeNumber,
            ScheduleExceptionCode,
            ExceptionStartedAt,
            ExceptionEndedAt,
            LogonID,
            RecordCreatedAt,
            RecordModifiedAt,
            RecordExpiredAt,
            AgentTypeCode
        )
        VALUES (
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s
        )
        ON DUPLICATE KEY UPDATE
            ScheduledAt = VALUES(ScheduledAt),
            AgentDepartmentCode = VALUES(AgentDepartmentCode),
            CustomerServiceManager = VALUES(CustomerServiceManager),
            AgentDepartmentName = VALUES(AgentDepartmentName),
            AgentEmployeeNumber = VALUES(AgentEmployeeNumber),
            ScheduleExceptionCode = VALUES(ScheduleExceptionCode),
            ExceptionStartedAt = VALUES(ExceptionStartedAt),
            ExceptionEndedAt = VALUES(ExceptionEndedAt),
            LogonID = VALUES(LogonID),
            RecordModifiedAt = VALUES(RecordModifiedAt),
            RecordExpiredAt = VALUES(RecordExpiredAt),
            AgentTypeCode = VALUES(AgentTypeCode)
    """

    cursor = conn.cursor()
    # cursor.execute("SET FOREIGN_KEY_CHECKS=0;")

    # delete operation start
    cursor.execute("SET FOREIGN_KEY_CHECKS=0;")
    if len(scheduledat_dates_general) > 0:
        logger.info(f"Deleting {len(scheduledat_dates_general)} general agent schedule records")
        deleteTransactionQry = "DELETE FROM AgentScheduleTransaction WHERE AgentTypeCode = 'A' AND AgentScheduleScheduledAt IN (%s)"
        deleteTransactionStatus = cursor.executemany(deleteTransactionQry, scheduledat_dates_general)
        logger.info("Deleted agent schedule transactions")

        deleteScheduleQry = "DELETE FROM AgentSchedule WHERE AgentTypeCode = 'A' AND ScheduledAt IN (%s)"
        deleteScheduleStatus = cursor.executemany(deleteScheduleQry, scheduledat_dates_general)
        logger.info("Deleted agent schedules")

    if len(scheduledate_dates_loyalty) > 0:
        logger.info(f"Deleting {len(scheduledate_dates_loyalty)} loyalty agent schedule records")
        deleteTransactionQry = "DELETE FROM AgentScheduleTransaction WHERE AgentTypeCode = 'L' AND AgentScheduleScheduledAt IN (%s)"
        deleteTransactionStatus = cursor.executemany(deleteTransactionQry, scheduledate_dates_loyalty)
        logger.info("Deleted loyalty agent schedule transactions")

        deleteScheduleQry = "DELETE FROM AgentSchedule WHERE AgentTypeCode = 'L' AND ScheduledAt IN (%s)"
        deleteScheduleStatus = cursor.executemany(deleteScheduleQry, scheduledate_dates_loyalty)
        logger.info("Deleted loyalty agent schedules")
    cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
    # delete operation end

    cursor.executemany(insert_qry, records)
    # cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
    conn.commit()
    cursor.close()
    logger.info("run script to database success: ")
    return

def read_data_collections_and_execute_db_transactions(data_collections, logger, conn):
    logger.info("Transaction processing started")
    # transaction
    transaction = []
    transaction_id = uuid.uuid4()
    transaction_tuple = (
        transaction_id,
        'Schedule',
        'Schedule'
    )
    transaction.append(transaction_tuple)

    # transaction_schedule
    records = []
    for index, record in enumerate(data_collections):
        agent_schedule_record = {}

        try:
            agent_schedule_record["ScheduledAt"] = (
                convert_date_string_to_timestamp(str(record.scheddate),"%m%d%y")
                if record["scheddate"] and bool(record.scheddate) == True
                else None
            )
        except Exception as err:
            logger.error(f"Transactions - field scheddate value not proper skipping: {err}")
            agent_schedule_record["ScheduledAt"] = None

        agent_schedule_record["AgentEmployeeNumber"] = (
            record.externalid.strip() if record["externalid"] and bool(record.externalid) == True else None
        )

        if agent_schedule_record["AgentEmployeeNumber"] is None or agent_schedule_record["AgentEmployeeNumber"] == '':
            continue

        agent_schedule_record["ScheduleExceptionCode"] = (
            record.exception.strip()
            if record["exception"] and bool(record.exception.strip()) == True
            else None
        )

        try:
            agent_schedule_record["AgentScheduleExceptionStartedAt"] = (
                convert_date_string_to_timestamp(
                    str(record.excdate) + ' ' + str(record.start), "%m%d%y %H:%M")
                if record["start"] and bool(record.start) == True
                else None
            )
        except Exception as err:
            logger.error(f"Transactions - field excdate or start value not proper skipping: {err}")
            agent_schedule_record["AgentScheduleExceptionStartedAt"] = None

        if agent_schedule_record["ScheduledAt"] is None or agent_schedule_record["AgentScheduleExceptionStartedAt"] is None:
            continue

        # set AgentTypeCode as per file e.g Loyalty or General
        filename = (
            record.filename if record["filename"] and bool(record.filename) == True else None
        )
        agent_type_code = 'A'
        if "LoyaltyAgentScheduleDetailFile" in filename:
            agent_type_code = 'L'
        agent_schedule_record["AgentTypeCode"] = agent_type_code

        agent_schedule_record["RecordExpiredAt"] = agent_schedule_record["ScheduledAt"] + datetime.timedelta(days=365)

        record_value = (
            transaction_id,
            agent_schedule_record["AgentEmployeeNumber"],
            agent_schedule_record["ScheduledAt"],
            agent_schedule_record["ScheduleExceptionCode"],
            agent_schedule_record["AgentScheduleExceptionStartedAt"],
            agent_schedule_record["AgentTypeCode"],
            agent_schedule_record["RecordExpiredAt"]
        )
        records.append(record_value)

    transaction_schedule = list(set(records))
    logger.info(f"Processing {len(transaction_schedule)} unique schedule transaction records")

    # Transaction
    insert_qry = """
        INSERT IGNORE INTO Transaction (
            `Id`,
            `Schema`,
            `TransactionTypeCode`
        )
        VALUES (
            %s,
            %s,
            %s
        ) 
    """

    cursor = conn.cursor()
    cursor.executemany(insert_qry, transaction)
    logger.info("Transaction ingestion done")

    # AgentScheduleTransaction
    insert_qry = """
        INSERT IGNORE INTO AgentScheduleTransaction (
            TransactionId,
            AgentEmployeeNumber,
            AgentScheduleScheduledAt,
            ScheduleExceptionCode,
            AgentScheduleExceptionStartedAt,
            AgentTypeCode,
            RecordExpiredAt
        )
        VALUES (
            %s,
            %s,
            %s,          
            %s,
            %s,
            %s,
            %s
        )  
    """
    cursor.executemany(insert_qry, transaction_schedule)
    conn.commit()
    cursor.close()
    logger.info("AgentScheduleTransaction ingestion done")
    return

def get_aad_secret():
    # Get AAD Secret details
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION)
    try:
        get_secret_value_response = client.get_secret_value(SecretId=AAD_SECRET_MANAGE)
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException" or e.response["Error"]["Code"] == "InternalServiceErrorException" or e.response["Error"]["Code"] == "InvalidParameterException" or e.response["Error"]["Code"] == "InvalidRequestException" or e.response["Error"]["Code"] == "ResourceNotFoundException":
            raise e
    else:
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            secret = json.loads(secret)
            client_id = secret["clientId"]
            client_secret = secret["clientSecret"]            
            return client_id, client_secret

def get_secret():
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION)
    try:
        get_secret_value_response = client.get_secret_value(SecretId=RDS_SECRET_MANAGE)
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException" or e.response["Error"]["Code"] == "InternalServiceErrorException" or e.response["Error"]["Code"] == "InvalidParameterException" or e.response["Error"]["Code"] == "InvalidRequestException" or e.response["Error"]["Code"] == "ResourceNotFoundException":
            raise e
    else:
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            secret = json.loads(secret)
            jdbc_username = secret["username"]
            jdbc_password = secret["password"]
            jdbc_url = f"jdbc:{secret['engine']}://{secret['hostProxyWR']}:{secret['port']}/{RDS_DATABASE_NAME}"
            return jdbc_username, jdbc_password, jdbc_url

def create_connection():
    JDBC_USERNAME, JDBC_PASSWORD, JDBC_URL = get_secret()
    mysql_url = JDBC_URL
    mysql_url = mysql_url.split("/")[2].split(":")[0]
    mysql_user = JDBC_USERNAME
    mysql_password = JDBC_PASSWORD
    database = RDS_DATABASE_NAME
    conn = MySQLdb.connect(
        host=mysql_url, user=mysql_user, password=mysql_password, database=database
    )
    conn.autocommit = False
    return conn

def read_new_agent_data_collections_and_execute_db(data_collections):
    conn = create_connection()
    cursor = conn.cursor()

    agent_data = data_collections;
    agent_insert_qry = """
        INSERT INTO Agent (
            Email,
            EmployeeNumber, 
            FirstName,
            LastName
        )
        VALUES (
            %s,
            %s,
            %s,
            %s
        )
        ON DUPLICATE KEY UPDATE 
            Email=VALUES(Email),
            FirstName=VALUES(FirstName),
            LastName=VALUES(LastName)
    """
    cursor.executemany(agent_insert_qry, agent_data)
    conn.commit()
    cursor.close()
    conn.close()
    return

def get_token(aad_client_id, aad_client_secret):
    
    params = {
        "grant_type": "client_credentials",
        "scope": AAD_SCOPE,
        "client_id": aad_client_id,
        "client_secret": aad_client_secret,
    }    
    params_keys = list(params.keys())
    data = '&'.join(list(map(lambda key: key + '=' + urllib.parse.quote(params[key]), params_keys)))
    url = "https://login.microsoftonline.com/" + AAD_TENENT_ID + "/oauth2/v2.0/token"
    headers = {"content-type": "application/x-www-form-urlencoded"}

    response = requests.post(url, headers=headers, data=data)
    response_data = json.loads(response.text)

    return response_data["access_token"]

def get_agents(token, logger):
    agents = []
    url = AAD_ENDPOINT
    headers = {
        'Authorization': 'Bearer ' + token
    }
    params = {
        "$select": "givenName,surname,jobTitle,displayName,userPrincipleName,employeeId,mail,id",
        '$filter': "startswith(employeeId,'AC')"
    }
    while url:
        try:
            if len(agents) == 0:
                response = requests.get(url, headers=headers, params=params)
            else:
                response = requests.get(url,headers=headers)
            data = response.json()
            if data['value'] is not None:
                agents += data['value']
                url = data.get('@odata.nextLink') #url for get next agent list
            else:
                logger.info("No more agent data to retrieve")
                break
        except Exception as e:
            logger.error(f'Exception getting agents: {e}')
            logger.info(f'URL: {url}')
            break

    return agents

def process_partition(partition_data, logger):
    """Process a single partition of data"""
    records = list(partition_data)
    no_of_records = len(records)
    logger.info(f"Processing {no_of_records} records")
    if no_of_records:
        conn = create_connection()
        try:
            read_data_collections_and_execute_db(partition_data, logger, conn)
            read_data_collections_and_execute_db_transactions(partition_data, logger, conn)
        finally:
            if conn:
                conn.close()

def process_agent_data(data, new_agent_ids_list):
    
    new_agent_ids_records = []
    for index, record in enumerate(new_agent_ids_list):
        new_agent_ids_records.append(record.externalID)
    processed_agent_data_list = []
    for agent in data:
        agent_obj = {}
        employee_id_check = agent["employeeId"]
        if employee_id_check in new_agent_ids_records:
            agent_obj["Email"] = agent["mail"]
            agent_obj["EmployeeNumber"] = agent["employeeId"]
            agent_obj["FirstName"] = agent["givenName"]
            agent_obj["LastName"] = agent["surname"]

            if agent_obj["Email"] is None or agent_obj["Email"] == '':
                continue

            agent_tuple = (
                agent_obj["Email"],
                agent_obj["EmployeeNumber"],
                agent_obj["FirstName"],
                agent_obj["LastName"]
            )
            processed_agent_data_list.append(agent_tuple)
    return processed_agent_data_list

def main():
    """Extract data from the data catalog and upsert to DB"""
    glue_context, spark = setup_context()
    logger = get_logger()
    try:
        # Get agent schedule details from csv file
        logger.info("Loading source data")
        source_df = get_source_df()
        source_df_count = source_df.count()
        logger.info(f"source_df count: {source_df_count}")

        if not source_df.isEmpty():
            # Process source data
            logger.info("Processing source data")
            source_csv_data_df = source_df.withColumn('externalid', 
                when((instr(col('externalid'), 'AC') == 1) | (col('externalid') == ''), 
                     col('externalid')).otherwise(concat(lit('AC'),col('externalid'))))
            logger.info(f"Data processing completed")

            # File processing and data filtering
            logger.info("File processing and filtering")
            source_csv_data_df = source_csv_data_df.withColumn("filename", input_file_name())
            source_csv_data_df.createOrReplaceTempView("AgentSchedule")
            source_df = glue_wrapper.execute_spark_sql(
                "SELECT * FROM AgentSchedule WHERE externalID <> 'ACexternalID' AND externalID <> 'ACtvID'"
            ).repartition(400)

            # Get Existing Agent Details from Db
            logger.info("Loading agent data")
            ccm_db_agent_df = get_ccm_db_agent_df()
            ccm_db_agent_df.createOrReplaceTempView("Agent")

            # Identify new agent employeeId from CSV file which not exist in Db agent table
            logger.info("Identifying new agents")
            diff_df = glue_wrapper.execute_spark_sql("SELECT DISTINCT AgentSchedule.externalID FROM AgentSchedule LEFT JOIN Agent ON AgentSchedule.externalID = Agent.EmployeeNumber WHERE Agent.EmployeeNumber IS NULL AND AgentSchedule.externalID <> 'ACexternalID' AND AgentSchedule.externalID <> 'ACtvID'")
            
            # Get count without collecting data to driver
            new_agents_count = diff_df.count()
            logger.info(f"Found {new_agents_count} new agents to process")

            # Check agent if not exist then fetch & insert relevant details into agent table
            if new_agents_count > 0:
                logger.info("Processing new agents")
                new_agent_ids_list = diff_df.collect()
                AAD_CLIENT_ID, AAD_CLIENT_SECRET = get_aad_secret()
                token = get_token(AAD_CLIENT_ID, AAD_CLIENT_SECRET)
                agent_data = get_agents(token, logger)
                processed_agent_data_list = process_agent_data(agent_data, new_agent_ids_list)
                logger.info(f"processed total {len(processed_agent_data_list)} agents")

                read_new_agent_data_collections_and_execute_db(processed_agent_data_list)
                logger.info(f"New agent ingestion completed")
            
            if source_df.head(1):
                logger.info("Processing agent schedule partitions")
                source_df.foreachPartition(lambda partition: process_partition(partition, logger))
                logger.info("Successfully processed all agent schedule partitions")
            else:
                logger.info("No data to ingest")

        else:
            logger.info("No data to process")

    except Exception as err:
        logger.info(f"Exception Occurred Transform Agent Schedule Data Job: {err}")
        job_attempt = get_job_attempt(JOB_RUN_ID)
        if job_attempt == "2":
            response = send_error_to_queue(err)
            logger.info(f"response: {response}")
        raise Exception(f"Exception Occurred Transform Agent Schedule Data Job: {err}")

    glue_wrapper.commit()


if __name__ == "__main__":
    main()
