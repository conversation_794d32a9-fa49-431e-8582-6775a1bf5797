Type: "AWS::IAM::Role"
Properties:
  RoleName: ${self:custom.iam.textToCSVJobRole.name}
  Tags:
    - Key: "aws-service"
      Value: "IAM Role"
    - Key: "unique-id"
      Value: ${self:custom.iam.textToCSVJobRole.name}
  AssumeRolePolicyDocument:
    Version: "2012-10-17"
    Statement:
      -
        Effect: "Allow"
        Principal:
          Service:
            - "lambda.amazonaws.com"
        Action:
          - "sts:AssumeRole"
  Path: "/"
  Policies:
    - 
      PolicyName: "S3BucketAccessPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - 
            Effect: "Allow"
            Action:
              - "s3:GetObject"
              - "s3:PutObject"
            Resource:
              - "arn:aws:s3:::${param:CCM_BATCH_INGESTION_BUCKET_NAME}/*"

    -
      PolicyName: "SQSSendMessage"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "sqs:SendMessage"
            Resource:
              - !Sub arn:aws:sqs:${self:provider.region}:${AWS::AccountId}:${self:custom.service}-schedule-text-to-csv-dlq-failed-queue-${self:provider.stage}

    -
      PolicyName: "XRayPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "xray:PutTraceSegments"
              - "xray:PutTelemetryRecords"
            Resource:
              - "*"
    -
      PolicyName: "NetworkInterface"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "ec2:CreateNetworkInterface"
              - "ec2:DescribeNetworkInterfaces"
              - "ec2:DescribeVpcs"
              - "ec2:DeleteNetworkInterface"
              - "ec2:DescribeSubnets"
              - "ec2:DescribeSecurityGroups"
            Resource:
              - "*"
    -
      PolicyName: "WriteLogPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: ${param:CW_PERMISSION}
            Action:
              - "logs:CreateLogGroup"
              - "logs:CreateLogStream"
              - "logs:PutLogEvents"
            Resource:
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:functions.text_to_csv.name}"
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:functions.text_to_csv.name}:*"
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:functions.text_to_csv.name}:*:*"
          