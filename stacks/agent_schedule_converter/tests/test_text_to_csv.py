import unittest
import os
from unittest.mock import patch, Mo<PERSON>, MagicMock
import datetime
import pytest

class MockEnv:
    @pytest.fixture(autouse=True)
    def env_setup(self):
        os.environ['REGION_NAME'] = 'mocktest'
        os.environ['ERROR_QUEUE_URL'] = 'https://sqs.mocktest.amazonaws.com/mocktest/mocktest'
        os.environ['JOB_NAME'] = 'ac-odh-batch-ingestion-ccm-schedule-converter-text-to-csv-mocktest'

class TestSendErrorToQueue(unittest.TestCase, MockEnv):
    @patch("boto3.client")
    def test_send_error_to_queue(self, mock_sqs_client):
        from stacks.agent_schedule_converter.scripts.text_to_csv import (
            send_error_to_queue,
        )

        client_instance = MagicMock()
        client_instance.send_message.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200}
        }
        mock_sqs_client.return_value = client_instance
        ex = Exception("Test Exception")
        response = send_error_to_queue(ex)
        self.assertEqual(response, {"ResponseMetadata": {"HTTPStatusCode": 200}})
        client_instance.send_message.assert_called_with(
            QueueUrl="https://sqs.mocktest.amazonaws.com/mocktest/mocktest",
            DelaySeconds=5,
            MessageAttributes={
                "Title": {
                    "DataType": "String",
                    "StringValue": "Error From Lambda ac-odh-batch-ingestion-ccm-schedule-converter-text-to-csv-mocktest",
                },
                "Author": {"DataType": "String", "StringValue": "Lambda Job"},
                "Date": {
                    "DataType": "String",
                    "StringValue": datetime.datetime.now().strftime("%m/%d/%Y"),
                },
            },
            MessageBody=str(ex),
        )

class TestHandler(unittest.TestCase, MockEnv):

    @patch("boto3.client")
    @patch(
        "stacks.agent_schedule_converter.scripts.text_to_csv.s3c.get_object"
    )
    @patch(
        "stacks.agent_schedule_converter.scripts.text_to_csv.pd.read_csv"
    )
    @patch(
        "stacks.agent_schedule_converter.scripts.text_to_csv.s3c.put_object"
    )
    @patch(
        "stacks.agent_schedule_converter.scripts.text_to_csv.io.BytesIO"
    )
    def test_handler(self, mock_client, mock_get_object, mock_read_csv, mock_put_object, mock_BytesIO):
        from stacks.agent_schedule_converter.scripts.text_to_csv import handler

        mock_get_object.return_value = MagicMock()
        mock_BytesIO.return_value = MagicMock()
        mock_read_csv.return_value = MagicMock()
        mock_read_csv.rename.return_value = MagicMock()
        mock_read_csv.to_csv.return_value = MagicMock()
        mock_put_object.return_value = MagicMock()

        event = {
            "Records": [
                {
                    "eventVersion": "2.1",
                    "eventSource": "aws:s3",
                    "awsRegion": "ca-central-1",
                    "eventTime": "2023-03-29T14:30:09.077Z",
                    "eventName": "ObjectCreated:Put",
                    "userIdentity": {
                        "principalId": "AWS:AROASNK2ZHHK4HQXEJH5B:mocktest"
                    },
                    "requestParameters": {
                        "sourceIPAddress": "************"
                    },
                    "responseElements": {
                        "x-amz-request-id": "RAAGQM1WAFKAQEAK",
                        "x-amz-id-2": "xu10Ea36n81It8fBivHUygrXGbW7Nt5T10h3wgBchYRHX9PA4GqtaryQENgBxQUtYFWXht5JwQpJiQZsMJ3MBlq4QlsIkgjTq7cu/8gw3mo="
                    },
                    "s3": {
                        "s3SchemaVersion": "1.0",
                        "configurationId": "mocktest-662f1322f50eb1afeb2ca99e367be864",
                        "bucket": {
                            "name": "mocktest",
                            "ownerIdentity": {
                                "principalId": "A190IHUQFO895I"
                            },
                            "arn": "arn:aws:s3:::mocktest"
                        },
                        "object": {
                            "key": "data-ingestion/ccm/agent_schedule/raw/moctest.txt",
                            "size": 1025,
                            "eTag": "ebc4b9ffee7d3a15c5c890ba6db1fd95",
                            "sequencer": "0064244B710CDF86D2"
                        }
                    }
                }
            ]
        }

        handler(event, {})
        mock_put_object.assert_called_once()
