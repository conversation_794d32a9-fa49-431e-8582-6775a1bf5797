DependsOn: Database
Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref Database
  TableInput:
    Name: agent_schedule
    TableType: EXTERNAL_TABLE
    StorageDescriptor:
      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CCM_BATCH_INGESTION_BUCKET_NAME}/${self:custom.defaultParams.${self:provider.stage}.AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME}
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      Columns:
        - Name: excdate
          Type: string
        - Name: scheddate
          Type: string
        - Name: muid
          Type: string
        - Name: customer service mgr
          Type: string
        - Name: department
          Type: string
        - Name: agentname
          Type: string
        - Name: externalid
          Type: string
        - Name: exception
          Type: string
        - Name: start
          Type: string
        - Name: stop
          Type: string
        - Name: logonid
          Type: string
      SerdeInfo:
        SerializationLibrary: org.apache.hadoop.hive.serde2.OpenCSVSerde
        Parameters:
          separatorChar: ","
          quoteChar: "\""
          escapeChar: "\\"
          serialization.null.format: ""
          skip.header.line.count: "1"