const path = require('path')
const slsw = require('serverless-webpack')
const nodeExternals = require('webpack-node-externals')

module.exports = (dirname) => ({
  target: 'node',
  // entry: slsw.lib.entries,
  entry: path.resolve(dirname, 'handler.js'),
  mode: slsw.lib.webpack.isLocal ? 'development' : 'production',
  node: false,
  optimization: {
    minimize: false
  },
  devtool: 'inline-cheap-module-source-map',
  output: {
    libraryTarget: 'commonjs2',
    filename: 'handler.js',
    path: path.join(dirname, '.webpack')
  },
  externals: [nodeExternals(
    {
      modulesFromFile: true,
    },
  )],
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', 'json'],
    alias: {
      '@common': path.resolve(__dirname, '../common/')
    }
  }
})
