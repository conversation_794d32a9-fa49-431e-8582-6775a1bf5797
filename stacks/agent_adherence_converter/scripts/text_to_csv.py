import json
import boto3
import pandas as pd
import io
from io import StringIO
import os
import datetime

REGION_ADHERENCE = os.environ['REGION_NAME']
ERROR_QUEUE_URL_ADHERENCE = os.environ['ERROR_QUEUE_URL']
JOB_NAME_ADHERENCE = os.environ['JOB_NAME']

s3c = boto3.client('s3', region_name=REGION_ADHERENCE)

def send_error_to_queue_adherence(ex):
    sqs_adherence = boto3.client("sqs", endpoint_url=f"https://sqs.{REGION_ADHERENCE}.amazonaws.com", region_name=REGION_ADHERENCE)
    time_adherence = datetime.datetime.now()
    sqs_adherence_response = sqs_adherence.send_message(
        QueueUrl=ERROR_QUEUE_URL_ADHERENCE,
        DelaySeconds=5,
        MessageAttributes={
            "Title": {
                "DataType": "String",
                "StringValue": "Error From Adherence Lambda " + JOB_NAME_ADHERENCE,
            },
            "Author": {"DataType": "String", "StringValue": "Lambda Adherence Job"},
            "Date": {"DataType": "String", "StringValue": time_adherence.strftime("%m/%d/%Y")},
        },
        MessageBody=(str(ex)),
    )
    return sqs_adherence_response

def handler_adherence(event, context):

    try:
        bucket_name = event['Records'][0]['s3']['bucket']['name']
        source_file_key = event['Records'][0]['s3']['object']['key']
        obj = s3c.get_object(Bucket=bucket_name, Key=source_file_key)
        emp_df = pd.read_csv(io.BytesIO(obj['Body'].read()), sep='|', skiprows=range(
            1, 2), encoding='unicode_escape', parse_dates=['#fields:date'], on_bad_lines='skip')
        emp_df['#fields:date'] = pd.to_datetime(emp_df['#fields:date'], errors='coerce', utc=True).dt.strftime('%m%d%y')
        emp_df.rename(columns={"#fields:date": "date"}, inplace=True)

        filenamefull = source_file_key.rsplit('/', 1)[1]
        filename = filenamefull.rsplit('.', 1)[0]

        csv_buffer = StringIO()
        emp_df.to_csv(csv_buffer, index=False)
        destination_file_key = 'data-ingestion/ccm/agent_adherence/formatted/' + filename + '.csv'
        response = s3c.put_object(Body=csv_buffer.getvalue(),
                                Bucket=bucket_name,
                                Key=destination_file_key)

        return response

    except Exception as err:
        if str(err) == "No columns to parse from file":
            print(f"no data in file to process: {err}")
        else:
            response = send_error_to_queue_adherence(err)
            print(err)
            print(response)
            raise Exception(f"Exception Occurred in Agent Adherence Text To CSV Lambda: {err}")
