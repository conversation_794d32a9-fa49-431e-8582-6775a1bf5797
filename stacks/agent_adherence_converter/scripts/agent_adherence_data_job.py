import sys
import os
import logging
import datetime
import boto3
import json

from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from botocore.exceptions import ClientError
from pyspark.sql.functions import col, when, instr, input_file_name, concat, lit
# Import glue-utils
from glue_utils.core.glue_context_wrapper import GlueContextWrapper

import urllib.parse
import requests
import uuid

# SQL
import pymysql

pymysql.install_as_MySQLdb()
import MySQLdb

# Initialize GlueContextWrapper instead of standard context
glue_wrapper = GlueContextWrapper()

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "CCM_BATCH_INGESTION_BUCKET_NAME",
        "AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME",
        "REGION",
        "ACCOUNT_ID",
        "ERROR_QUEUE",
        "RDS_DATABASE_NAME",
        "RDS_SECRET_MANAGE",
        "AAD_SECRET_MANAGE",
        "AAD_TENENT_ID",
        "AAD_SCOPE",
        "AAD_ENDPOINT"
    ],
)

JOB_NAME = args["JOB_NAME"]
JOB_RUN_ID = args["JOB_RUN_ID"]
CCM_BATCH_INGESTION_BUCKET_NAME = args["CCM_BATCH_INGESTION_BUCKET_NAME"]
AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME = args[
    "AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME"
]
REGION = args["REGION"]
ACCOUNT_ID = args["ACCOUNT_ID"]
ERROR_QUEUE = args["ERROR_QUEUE"]
RDS_DATABASE_NAME = args["RDS_DATABASE_NAME"]
RDS_SECRET_MANAGE = args["RDS_SECRET_MANAGE"]
AAD_SECRET_MANAGE = args["AAD_SECRET_MANAGE"]
AAD_TENENT_ID = args["AAD_TENENT_ID"]
AAD_SCOPE = args["AAD_SCOPE"]
AAD_ENDPOINT = args["AAD_ENDPOINT"]
JDBC_USERNAME = ""
JDBC_PASSWORD = ""
JDBC_URL = ""
AAD_CLIENT_ID = ''
AAD_CLIENT_SECRET = ''

def get_logging_level_adherence():
    env_val = os.environ.get("LOGLEVEL", "INFO")
    return logging.getLevelName(env_val)


def get_logger_adherence():
    logger = logging.getLogger(__name__)
    logger.setLevel(get_logging_level_adherence())

    formatter = logging.Formatter(
        fmt="[%(filename)s:%(lineno)s - %(funcName)10s() ] %(asctime)-15s %(message)s",
        datefmt="%Y-%m-%d:%H:%M:%S",
    )

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


def send_error_to_queue_adherence(ex):
    sqs = boto3.client(
        "sqs", endpoint_url=f"https://sqs.{REGION}.amazonaws.com", region_name=REGION
    )
    time = datetime.datetime.now()
    queue_url = f"https://sqs.{REGION}.amazonaws.com/{ACCOUNT_ID}/{ERROR_QUEUE}"
    response = sqs.send_message(
        QueueUrl=queue_url,
        DelaySeconds=5,
        MessageAttributes={
            "Title": {
                "DataType": "String",
                "StringValue": "Error From Glue Job " + JOB_NAME,
            },
            "Author": {"DataType": "String", "StringValue": "Glue Job"},
            "Date": {"DataType": "String", "StringValue": time.strftime("%m/%d/%Y")},
        },
        MessageBody=(str(ex)),
    )
    return response


def get_job_attempt_adherence(job_id: str) -> str:
    attempt_count = "0"
    if "attempt" in job_id:
        attempt_count = job_id.split("attempt_")[-1]

    return attempt_count


def get_source_df_adherence():
    s3_path = f"s3://{CCM_BATCH_INGESTION_BUCKET_NAME}/{AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME}/"
    return glue_wrapper.load_data(
        source_type="S3",
        connection_options={
            "path": s3_path,
            "format": "csv",
            "format_options":{
                "withHeader": True,
                "separator": ","
            },
            "transformation_ctx":"source_df"
        }
    )

def get_ccm_db_agent_df_adherence():
    JDBC_USERNAME, JDBC_PASSWORD, JDBC_URL = get_secret_adherence()
    sqlQuery = "SELECT EmployeeNumber FROM CcmDataStoreV2.Agent WHERE EmployeeNumber LIKE 'AC%'"
    return glue_wrapper.load_data(
        source_type="JDBC",
        query=sqlQuery,
        fetch_size=100000,
        # database="CcmDataStoreV2",
        # table_name="Agent",
        connection_options={
            "url": JDBC_URL,
            "user": JDBC_USERNAME,
            "password": JDBC_PASSWORD,
            "dbtable": "Agent",
            "driver": "com.mysql.cj.jdbc.Driver",
            "disableUrlUpdate":"true", 
            "zeroDateTimeBehavior": "convertToNull"
        }
    )

def convert_date_string_to_timestamp_adherence(time, format):
    if time is None:
        return None
    datetime_str = str(time).zfill(6)
    date_time = datetime.datetime.strptime(datetime_str, format)
    return date_time


def setup_context_adherence():
    spark = glue_wrapper.spark
    sc = spark.sparkContext
    glue_context = GlueContext(sc)

    spark.conf.set("spark.sql.execution.arrow.enabled", "true")

    return (glue_context, spark)

def read_data_collections_and_execute_db_adherence(data_collections, logger, conn):
    records = []
    scheduledat_dates_general = []
    scheduledate_dates_loyalty = []
    for index, record in enumerate(data_collections):
        agent_adherence_record = {}
        try:
            agent_adherence_record["ScheduledAt"] = (
                convert_date_string_to_timestamp_adherence(str(record.date),"%m%d%y")
                if record["date"] and bool(record.date) == True
                else None
            )
        except Exception as err:
            logger.info(f"Adherence - field date value not proper skipping: {err}")
            agent_adherence_record["ScheduledAt"] = None

        if agent_adherence_record["ScheduledAt"] is None:
            continue

        agent_adherence_record["AgentDepartmentCode"] = (
            record.muid if record["muid"] and bool(record.muid) == True else None
        )
        agent_adherence_record["AgentEmployeeNumber"] = (
            record.externalid.strip() if record["externalid"] and bool(record.externalid) == True else None
        )

        if agent_adherence_record["AgentEmployeeNumber"] is None or agent_adherence_record["AgentEmployeeNumber"] == '':
            continue

        agent_adherence_record["LogonId"] = (
            record.logonid
            if record["logonid"] and bool(record.logonid) == True
            else None
        )
        agent_adherence_record["AgentDepartmentName"] = (
            record.department.strip()
            if record["department"] and bool(record.department.strip()) == True
            else None
        )
        agent_adherence_record["CustomerServiceManager"] = (
            record["customer service mgr"].strip()
            if record["customer service mgr"]
            and bool(record["customer service mgr"].strip()) == True
            else None
        )
        agent_adherence_record["TotalActions"] = (
            record.totalact
            if record["totalact"] and bool(record.totalact) == True
            else None
        )
        agent_adherence_record["TotalInAdherence"] = (
            record.totalinadh
            if record["totalinadh"] and bool(record.totalinadh) == True
            else None
        )
        agent_adherence_record["TotalOutAdherence"] = (
            record.totaloutadh
            if record["totaloutadh"] and bool(record.totaloutadh) == True
            else None
        )
        agent_adherence_record["TotalSchedule"] = (
            record.totalsched
            if record["totalsched"] and bool(record.totalsched) == True
            else None
        )
        agent_adherence_record["AdherencePercentage"] = (
            record.adhpct if record["adhpct"] and bool(record.adhpct) == True else None
        )

        current_time = datetime.datetime.now()
        agent_adherence_record["RecordCreatedAt"] = current_time
        agent_adherence_record["RecordModifiedAt"] = current_time

        # set AgentTypeCode as per file e.g Loyalty or General
        filename = (
            record.filename if record["filename"] and bool(record.filename) == True else None
        )
        agent_type_code = 'A'
        if "LoyaltyAdherenceSummaryFile" in filename:
            agent_type_code = 'L'
        agent_adherence_record["AgentTypeCode"] = agent_type_code

        if agent_type_code == 'L':
            scheduledat_date = (agent_adherence_record["ScheduledAt"])
            if scheduledat_date not in scheduledate_dates_loyalty:
                scheduledate_dates_loyalty.append(scheduledat_date)
        else:
            scheduledat_date = (agent_adherence_record["ScheduledAt"])
            if scheduledat_date not in scheduledat_dates_general:
                scheduledat_dates_general.append(scheduledat_date)

        agent_adherence_record["RecordExpiredAt"] = scheduledat_date + timedelta(days=365)

        record_value = (
            agent_adherence_record["ScheduledAt"],
            agent_adherence_record["AgentDepartmentCode"],
            agent_adherence_record["AgentEmployeeNumber"],
            agent_adherence_record["LogonId"],
            agent_adherence_record["AgentDepartmentName"],
            agent_adherence_record["CustomerServiceManager"],
            agent_adherence_record["TotalActions"],
            agent_adherence_record["TotalInAdherence"],
            agent_adherence_record["TotalOutAdherence"],
            agent_adherence_record["TotalSchedule"],
            agent_adherence_record["AdherencePercentage"],
            agent_adherence_record["RecordCreatedAt"],
            agent_adherence_record["RecordModifiedAt"],
            agent_adherence_record["RecordExpiredAt"],
            agent_adherence_record["AgentTypeCode"],
        )
        records.append(record_value)

    insert_qry = """
        INSERT IGNORE INTO AgentAdherence (
            ScheduledAt,
            AgentDepartmentCode,
            AgentEmployeeNumber, 
            LogonId,
            AgentDepartmentName,
            CustomerServiceManager, 
            TotalActions, 
            TotalInAdherence, 
            TotalOutAdherence, 
            TotalSchedule,
            AdherencePercentage,
            RecordCreatedAt,
            RecordModifiedAt,
            RecordExpiredAt,
            AgentTypeCode
        )
        VALUES (
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s,
            %s            
        )
        ON DUPLICATE KEY UPDATE
            ScheduledAt = VALUES(ScheduledAt),
            AgentDepartmentCode = VALUES(AgentDepartmentCode),
            AgentEmployeeNumber = VALUES(AgentEmployeeNumber),
            LogonId = VALUES(LogonId),
            AgentDepartmentName = VALUES(AgentDepartmentName),
            CustomerServiceManager = VALUES(CustomerServiceManager),
            TotalActions = VALUES(TotalActions),
            TotalInAdherence = VALUES(TotalInAdherence),
            TotalOutAdherence = VALUES(TotalOutAdherence),
            TotalSchedule = VALUES(TotalSchedule),
            AdherencePercentage = VALUES(AdherencePercentage),
            RecordModifiedAt = VALUES(RecordModifiedAt),
            RecordExpiredAt = VALUES(RecordExpiredAt),
            AgentTypeCode = VALUES(AgentTypeCode)
    """

    cursor = conn.cursor()

    # delete operation start
    cursor.execute("SET FOREIGN_KEY_CHECKS=0;")
    if scheduledat_dates_general:
        logger.info(f"delete start for AgentAdherenceTransaction. Dates[{scheduledat_dates_general}]")
        deleteTransactionQry = "DELETE FROM AgentAdherenceTransaction WHERE AgentTypeCode = 'A' AND AgentScheduledAt IN (%s)"
        deleteTransactionStatus = cursor.executemany(deleteTransactionQry, scheduledat_dates_general)
        logger.info(f"delete end for AgentAdherenceTransaction. Status[{deleteTransactionStatus}]")

        logger.info(f"delete start for AgentAdherence")
        deleteAdherenceQry = "DELETE FROM AgentAdherence WHERE AgentTypeCode = 'A' AND ScheduledAt IN (%s)"
        deleteAdherenceStatus = cursor.executemany(deleteAdherenceQry, scheduledat_dates_general)
        logger.info(f"delete operation end. Status[{deleteAdherenceStatus}]")

    if scheduledate_dates_loyalty:
        logger.info(f"delete start for AgentAdherenceTransaction. Dates[{scheduledate_dates_loyalty}]")
        deleteTransactionQry = "DELETE FROM AgentAdherenceTransaction WHERE AgentTypeCode = 'L' AND AgentScheduledAt IN (%s)"
        deleteTransactionStatus = cursor.executemany(deleteTransactionQry, scheduledate_dates_loyalty)
        logger.info(f"delete end for AgentAdherenceTransaction. Status[{deleteTransactionStatus}]")

        logger.info(f"delete start for AgentAdherence")
        deleteAdherenceQry = "DELETE FROM AgentAdherence WHERE AgentTypeCode = 'L' AND ScheduledAt IN (%s)"
        deleteAdherenceStatus = cursor.executemany(deleteAdherenceQry, scheduledate_dates_loyalty)
        logger.info(f"delete operation end. Status[{deleteAdherenceStatus}]")
    cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
    # delete operation end

    cursor.executemany(insert_qry, records)
    conn.commit()
    cursor.close()
    logger.info("run script to database success: ")
    return

def read_data_collections_and_execute_db_adherence_transactions(data_collections, logger, conn):
    logger.info("transaction processing started")

    # transaction
    transaction = []
    transaction_id = uuid.uuid4()
    transaction_tuple = (
        transaction_id,
        'Adherence',
        'Adherence'
    )
    transaction.append(transaction_tuple)

    # transaction_adherence
    # cursor.execute("SET FOREIGN_KEY_CHECKS=0;")
    records = []
    for index, record in enumerate(data_collections):
        agent_adherence_record = {}
        try:
            agent_adherence_record["ScheduledAt"] = (
                convert_date_string_to_timestamp_adherence(str(record.date),"%m%d%y")
                if record["date"] and bool(record.date) == True
                else None
            )
        except Exception as err:
            logger.info(f"Transaction - field date value not proper skipping: {err}")
            agent_adherence_record["ScheduledAt"] = None

        if agent_adherence_record["ScheduledAt"] is None:
            continue

        agent_adherence_record["AgentEmployeeNumber"] = (
            record.externalid.strip() if record["externalid"] and bool(record.externalid) == True else None
        )

        if agent_adherence_record["AgentEmployeeNumber"] is None or agent_adherence_record["AgentEmployeeNumber"] == '':
            continue

        # set AgentTypeCode as per file e.g Loyalty or General
        filename = (
            record.filename if record["filename"] and bool(record.filename) == True else None
        )
        agent_type_code = 'A'
        if "LoyaltyAdherenceSummaryFile" in filename:
            agent_type_code = 'L'
        agent_adherence_record["AgentTypeCode"] = agent_type_code

        agent_adherence_record["RecordExpiredAt"] = agent_adherence_record["ScheduledAt"] + timedelta(days=365)

        record_value = (
            transaction_id,
            agent_adherence_record["AgentEmployeeNumber"],
            agent_adherence_record["ScheduledAt"],
            agent_adherence_record["AgentTypeCode"],
            agent_adherence_record["RecordExpiredAt"]
        )
        records.append(record_value)

    transaction_adherence = list(set(records))

    # print(transaction)
    # print(transaction_adherence)

    # Transaction
    insert_qry = """
        INSERT IGNORE INTO Transaction (
            `Id`,
            `Schema`,
            `TransactionTypeCode`
        )
        VALUES (
            %s,
            %s,
            %s
        ) 
    """
    cursor = conn.cursor()
    cursor.executemany(insert_qry, transaction)
    logger.info("Transaction ingestion done")

    # AgentAdherenceTransaction
    insert_qry = """
        INSERT IGNORE INTO AgentAdherenceTransaction (
            TransactionId,
            AgentEmployeeNumber,
            AgentScheduledAt,
            AgentTypeCode,
            RecordExpiredAt
        )
        VALUES (
            %s,
            %s,
            %s,
            %s,
            %s
        )
    """
    cursor.executemany(insert_qry, transaction_adherence)
    conn.commit()
    cursor.close()
    logger.info("AgentAdherenceTransaction ingestion done")
    return

def get_aad_secret_adherence():
    # Get AAD Secret details
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION)
    try:
        get_secret_value_response = client.get_secret_value(SecretId=AAD_SECRET_MANAGE)        
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException" or e.response["Error"]["Code"] == "InternalServiceErrorException" or e.response["Error"]["Code"] == "InvalidParameterException" or e.response["Error"]["Code"] == "InvalidRequestException" or e.response["Error"]["Code"] == "ResourceNotFoundException":
            raise e
    else:
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            secret = json.loads(secret)
            client_id = secret["clientId"]
            client_secret = secret["clientSecret"]            
            return client_id, client_secret

def get_secret_adherence():
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION)
    try:
        get_secret_value_response = client.get_secret_value(SecretId=RDS_SECRET_MANAGE)
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException" or e.response["Error"]["Code"] == "InternalServiceErrorException" or e.response["Error"]["Code"] == "InvalidParameterException" or e.response["Error"]["Code"] == "InvalidRequestException" or e.response["Error"]["Code"] == "ResourceNotFoundException":
            raise e
    else:
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            secret = json.loads(secret)
            jdbc_username = secret["username"]
            jdbc_password = secret["password"]
            jdbc_url = f"jdbc:{secret['engine']}://{secret['hostProxyWR']}:{secret['port']}/{RDS_DATABASE_NAME}"
            return jdbc_username, jdbc_password, jdbc_url

def create_connection():
    JDBC_USERNAME, JDBC_PASSWORD, JDBC_URL = get_secret_adherence()
    mysql_url = JDBC_URL
    mysql_url = mysql_url.split("/")[2].split(":")[0]
    mysql_user = JDBC_USERNAME
    mysql_password = JDBC_PASSWORD
    database = RDS_DATABASE_NAME
    conn = MySQLdb.connect(
        host=mysql_url, user=mysql_user, password=mysql_password, database=database
    )
    conn.autocommit = False
    return conn

def read_new_agent_data_collections_and_execute_db_for_adherence(data_collections):
    agent_data = data_collections;
    agent_insert_qry = """
        INSERT INTO Agent (
            Email,
            EmployeeNumber, 
            FirstName,
            LastName
        )
        VALUES (
            %s,
            %s,
            %s,
            %s
        )
        ON DUPLICATE KEY UPDATE 
            Email=VALUES(Email),
            FirstName=VALUES(FirstName),
            LastName=VALUES(LastName)
    """
    conn = create_connection()
    cursor = conn.cursor()
    cursor.executemany(agent_insert_qry, agent_data)
    conn.commit()
    cursor.close()
    conn.close()
    return

def get_token_for_adherence(aad_client_id, aad_client_secret):
    
    params = {
        "grant_type": "client_credentials",
        "scope": AAD_SCOPE,
        "client_id": aad_client_id,
        "client_secret": aad_client_secret,
    }    
    params_keys = list(params.keys())
    data = '&'.join(list(map(lambda key: key + '=' + urllib.parse.quote(params[key]), params_keys)))
    url = "https://login.microsoftonline.com/" + AAD_TENENT_ID + "/oauth2/v2.0/token"
    headers = {"content-type": "application/x-www-form-urlencoded"}

    response = requests.post(url, headers=headers, data=data)
    response_data = json.loads(response.text)

    return response_data["access_token"]

def get_agents_for_adherence(token, logger):
    agents = []
    url = AAD_ENDPOINT
    headers = {
        'Authorization': 'Bearer ' + token
    }
    params = {
        "$select": "givenName,surname,jobTitle,displayName,userPrincipleName,employeeId,mail,id",
        '$filter': "startswith(employeeId,'AC')"
    }
    while url:
        try:
            if len(agents) == 0:
                response = requests.get(url, headers=headers, params=params)
            else:
                response = requests.get(url,headers=headers)
            data = response.json()
            if data['value'] is not None:
                agents += data['value']
                url = data.get('@odata.nextLink') #url for get next agent list
            else:
                logger.info()
        except Exception as e:
            logger.info('Exception Occurred While sending the request: '+ str(ex))
            logger.info('URL :' + url)            

    return agents

def process_partition_adherence(partition_data, logger):
    """Process a single partition of data"""
    records = list(partition_data)
    no_of_records = len(records)
    logger.info(f"Processing {no_of_records} records")
    if no_of_records:
        conn = create_connection()
        try:
            read_data_collections_and_execute_db_adherence(partition_data, logger, conn)
            read_data_collections_and_execute_db_adherence_transactions(partition_data, logger, conn)
        finally:
            if conn:
                conn.close()

def process_agent_data_for_adherence(data, new_agent_ids_list, logger):
    
    new_agent_ids_records = []
    for index, record in enumerate(new_agent_ids_list):
        new_agent_ids_records.append(record.externalID)
    logger.info(f"new_agent_ids_records: {len(new_agent_ids_records)}")
    
    processed_agent_data_list = []
    for agent in data:
        agent_obj = {}
        employee_id_check = agent["employeeId"]
        if employee_id_check in new_agent_ids_records:
            agent_obj["Email"] = agent["mail"]
            # agent_obj["DisplayName"] = agent["displayName"]
            agent_obj["EmployeeNumber"] = agent["employeeId"]
            agent_obj["FirstName"] = agent["givenName"]
            agent_obj["LastName"] = agent["surname"]

            if agent_obj["Email"] is None or agent_obj["Email"] == '':
                continue

            agent_tuple = (
                agent_obj["Email"],
                agent_obj["EmployeeNumber"],
                agent_obj["FirstName"],
                agent_obj["LastName"]
                # agent_obj["DisplayName"]
            )
            processed_agent_data_list.append(agent_tuple)
    return processed_agent_data_list

def main():
    """Extract data from the data catalog and upsert to DB"""
    glue_context, spark = setup_context_adherence()
    logger = get_logger_adherence()
    try:
        # Get agent adherence details from csv file
        source_df = get_source_df_adherence()
        if not source_df.isEmpty():
            # logger.info(f"Source DF size: {source_df.count()} rows")

            # remove header from data
            if not "pytest" in sys.modules:
                # update tvID value with 'AC' as prefix
                source_csv_data_df = source_df.withColumn('externalid', when((instr(col('externalid'), 'AC') == 1) | (col('externalid') == ''), col('externalid')).otherwise(concat(lit('AC'),col('externalid'))))

                source_csv_data_df = source_csv_data_df.withColumn("filename", input_file_name())

                # store dataframe into spark temp view
                source_csv_data_df.createOrReplaceTempView("AgentAdherence")
                source_df = glue_wrapper.execute_spark_sql(
                    "SELECT * FROM AgentAdherence WHERE externalID <> 'ACexternalID' AND externalID <> 'ACtvID'"
                ).repartition(200)

            # Get Existing Agent Details from Db
            ccm_db_agent_df = get_ccm_db_agent_df_adherence()
            logger.info(f"Agent table total {ccm_db_agent_df.count()} agents")
            ccm_db_agent_df.createOrReplaceTempView("Agent")

            # Identify new agent employeeId from CSV file which not exist in Db agent table
            diff_df = glue_wrapper.execute_spark_sql("SELECT DISTINCT AgentAdherence.externalID FROM AgentAdherence LEFT JOIN Agent ON AgentAdherence.externalID = Agent.EmployeeNumber WHERE Agent.EmployeeNumber IS NULL AND AgentAdherence.externalID <> 'ACexternalID' AND AgentAdherence.externalID <> 'ACtvID'")

            # Check agent if not exist then fetch & insert relevant details into agent table
            if diff_df:
                logger.info(f"process ingestion")   
                new_agent_ids_list = diff_df.collect()
                AAD_CLIENT_ID, AAD_CLIENT_SECRET = get_aad_secret_adherence()
                token = get_token_for_adherence(AAD_CLIENT_ID, AAD_CLIENT_SECRET)
                agent_data = get_agents_for_adherence(token, logger)            
                processed_agent_data_list = process_agent_data_for_adherence(agent_data, new_agent_ids_list, logger)
                logger.info(f"processed total {len(processed_agent_data_list)} agents")
                read_new_agent_data_collections_and_execute_db_for_adherence(processed_agent_data_list)

            logger.info(f"new agent ingestion flow finish here")

            # csv agent adherence details ingestion flow start here
            if source_df.head(1):
                logger.info("Processing agent schedule partitions")
                source_df.foreachPartition(lambda partition: process_partition_adherence(partition, logger))
                logger.info("Successfully processed all agent schedule partitions")
            else:
                logger.info("No data to ingest")

        else:
            logger.info("No data to process")

    except Exception as err:
        logger.info(f"Exception Occurred Transform Agent Adherence Data Job: {err}")
        job_attempt = get_job_attempt_adherence(JOB_RUN_ID)
        if job_attempt == "2":
            response = send_error_to_queue_adherence(err)
            logger.info(f"response: {response}")
        raise Exception(f"Exception Occurred Transform Agent Adherence DAta Job: {err}")

    glue_wrapper.commit()

if __name__ == "__main__":
    main()
