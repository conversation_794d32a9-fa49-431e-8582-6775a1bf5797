service: ${self:custom.service}-aadh-converter

params: ${file(../../defaults.yml):custom.params}

provider:
  name: aws
  runtime: python3.11
  architecture: arm64
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}
  environment:
    AWS_LAMBDA_EXEC_WRAPPER: ${self:custom.defaults.custom.params.${self:provider.stage}.AWS_LAMBDA_EXEC_WRAPPER}
    DT_TENANT: ${self:custom.defaults.custom.params.${self:provider.stage}.DT_TENANT}
    DT_CLUSTER_ID: ${self:custom.defaults.custom.params.${self:provider.stage}.DT_CLUSTER_ID}
    DT_CONNECTION_BASE_URL: ${self:custom.defaults.custom.params.${self:provider.stage}.DT_CONNECTION_BASE_URL}
    DT_CONNECTION_AUTH_TOKEN: ${self:custom.defaults.custom.params.${self:provider.stage}.DT_CONNECTION_AUTH_TOKEN}

plugins:
  - serverless-glue

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  iam:
    crawlerAgentAdherenceRawRole:
      name: ${self:custom.base}-raw-crl
    glueRole:
      name: ${self:custom.base}-gluerole
    textToCSVJobRole:
      name: ${self:custom.base}-tc-lr
  s3:
    GlueBucket:
      name: ${self:custom.base}-jobscript
  glue:
    JobName: ${self:custom.base}-job

resources:
  Resources:
    textToCSVJobRole: ${file(./resources/agentAdhLambdaTextToCSVJobRole.yml)}
    glueJobRole: ${file(./resources/agentAdhGlueJobRole.yml)}

functions:
  text_to_csv:
    name: ${self:service}-text-to-csv
    handler: scripts/text_to_csv.handler_adherence
    logRetentionInDays: ${param:CW_RETENTION_IN_DAY}
    role: textToCSVJobRole
    timeout: ${param:LAMBDA_TIMEOUT}
    memorySize: ${param:TRIGGER_MEMORY_SIZE}
    layers:
      - arn:aws:lambda:${self:provider.region}:************:layer:AWSSDKPandas-Python39:4
      - arn:aws:lambda:${self:provider.region}:************:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      ERROR_QUEUE_URL: !Sub https://sqs.${self:provider.region}.amazonaws.com/${AWS::AccountId}/${self:custom.service}-adherence-text-to-csv-dlq-failed-queue-${self:provider.stage}
      JOB_NAME: ${self:service}-text-to-csv
      REGION_NAME: ${self:provider.region}
      STATUS_EVENT: ${param:STATUS_EVENT}
    events:
      - s3:
          bucket: ${param:CCM_BATCH_INGESTION_BUCKET_NAME}
          event: s3:ObjectCreated:*
          existing: true
          rules:
            - prefix: ${param:AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME}

Glue:
  bucketDeploy: ${self:custom.s3.GlueBucket.name}
  tempDirBucket: ${self:custom.s3.GlueBucket.name}
  tempDirS3Prefix: glue-script/tmp
  s3Prefix: glue-script/
  createBucket: true
  jobs:
    - ${file(./resources/agentAdhGlueJob.yml)}
  triggers:
    - ${file(./resources/agentAdhGlueJobTrigger.yml)}
