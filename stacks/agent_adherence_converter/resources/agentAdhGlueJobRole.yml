Type: "AWS::IAM::Role"
Properties:
  RoleName: ${self:custom.iam.glueRole.name}
  Tags:
    - Key: "aws-service"
      Value: "IAM Role"
    - Key: "unique-id"
      Value: ${self:custom.iam.glueRole.name}
  AssumeRolePolicyDocument:
    Version: "2012-10-17"
    Statement:
      - 
        Effect: "Allow"
        Principal:
          Service:
            - "glue.amazonaws.com"
        Action:
          - "sts:AssumeRole"
  Path: "/"
  ManagedPolicyArns:
    ['arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole']
  Policies:
    - 
      PolicyName: "S3BucketAccessPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - 
            Effect: "Allow"
            Action:
              - "s3:GetObject"
            Resource:
              - "arn:aws:s3:::${self:custom.s3.GlueBucket.name}/*"
          - 
            Effect: "Allow"
            Action:
              - "s3:GetObject"
            Resource:
              - "arn:aws:s3:::${param:CCM_BATCH_INGESTION_BUCKET_NAME}-jobscript/*"
          -
            Effect: "Allow"
            Action:
              - "s3:PutObject"
              - "s3:GetObject"
              - "s3:DeleteObject"
              - "s3:DeleteObjectVersion"
            Resource:
              - "arn:aws:s3:::${param:CCM_BATCH_INGESTION_BUCKET_NAME}/*"
    - 
      PolicyName: "GlueAccessPolicyUpdate"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "glue:GetConnections"
              - "glue:GetTables"
              - "glue:GetDatabases"
            Resource:
              - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:catalog
              - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:database/ccm_agent_adherence_raw_db
              - !Sub arn:aws:glue:${self:provider.region}:${AWS::AccountId}:table/ccm_agent_adherence_raw_db/*
    - 
      PolicyName: "GlueAccessSecretManage"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "secretsmanager:GetSecretValue"
            Resource:
              - !Sub "arn:aws:secretsmanager:${self:provider.region}:${AWS::AccountId}:secret:/${self:provider.stage}/${param:RDS_SECRET_MANAGE}-*"
              - !Sub "arn:aws:secretsmanager:${self:provider.region}:${AWS::AccountId}:secret:${param:AAD_SECRET_MANAGE}-*"
    -
      PolicyName: "WriteLogPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "logs:CreateLogGroup"
              - "logs:CreateLogStream"
              - "logs:PutLogEvents"
            Resource:
              - "arn:aws:logs:*:*:/aws-glue/*"
    -
      PolicyName: "CloudwatchAccess"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "cloudwatch:PutMetricData"
            Resource:
              - "*"
    -
      PolicyName: "DLQFailJob"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "sqs:SendMessage"
            Resource:
              - !Sub arn:aws:sqs:${self:provider.region}:${AWS::AccountId}:${cf:${self:custom.service}-aadh-converter-dlq-${self:provider.stage}.GlueJobDLQName}