name: ${self:custom.glue.JobName}
scriptPath: scripts/agent_adherence_data_job.py
type: spark
glueVersion: python3-4.0
tempDir: true
role: !Ref glueJobRole
Connections:
  - ${cf:ac-odh-common-resources-glue-${self:provider.stage}.ConnectionNameCCMRDSCcmDataStoreV2ReaderProxy}
MaxConcurrentRuns: ${param:GLUE_JOB_CONFIG_MAXCONCURRENTRUNS}
WorkerType: ${param:GLUE_JOB_CONFIG_WORKERTYPE}
NumberOfWorkers: ${param:GLUE_JOB_CONFIG_NUMBEROFWORKERS}
Timeout: ${param:GLUE_JOB_CONFIG_TIMEOUT}
MaxRetries: ${param:GLUE_JOB_CONFIG_MAXRETRIES}
Tags:
  aws-service: "glue"
  unique-id: ${self:custom.glue.JobName}
DefaultArguments:
  jobBookmarkOption: job-bookmark-enable
  enableAutoScaling: true
  enableMetrics: true
  enableObservabilityMetrics: "true"
  enableSparkUi: "true"
  sparkEventLogsPath: ${self:custom.defaults.custom.params.${self:provider.stage}.GLUE_SPARK_UI_LOGS_PATH}
  extraPyFiles: ${self:custom.defaults.custom.params.${self:provider.stage}.GLUE_UTILS_PATH}
  customArguments:
    "--RDS_DATABASE_NAME": ${param:RDS_DATABASE_NAME}
    "--RDS_SECRET_MANAGE": /${self:provider.stage}/${param:RDS_SECRET_MANAGE}
    "--AAD_SECRET_MANAGE": ${param:AAD_SECRET_MANAGE}
    "--AAD_TENENT_ID": ${param:AAD_TENENT_ID}
    "--AAD_SCOPE": ${param:AAD_SCOPE}
    "--AAD_ENDPOINT": ${param:AAD_ENDPOINT}
    "--CCM_BATCH_INGESTION_BUCKET_NAME": ${param:CCM_BATCH_INGESTION_BUCKET_NAME}
    "--AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME": ${param:AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME}
    "--REGION": ${self:provider.region}
    "--ACCOUNT_ID": !Sub ${AWS::AccountId}
    "--STORE_DB_ENV": ${param:STORE_DB_ENV}
    "--ERROR_QUEUE": ${cf:${self:custom.service}-aadh-converter-dlq-${self:provider.stage}.GlueJobDLQName}