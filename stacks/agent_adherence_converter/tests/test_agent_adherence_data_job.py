import unittest
import pytest
import sys
from unittest.mock import MagicMock, patch, Mock
import datetime
from botocore.exceptions import ClientError


class dotdict(dict):
    """dot.notation access to dictionary attributes"""

    __getattr__ = dict.get
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__


class MockEnv:
    @pytest.fixture(autouse=True)
    def env_setup(self):
        sys.argv = [
            "",
            "--JOB_NAME=agent-adherence-converter-data-job",
            "--JOB_RUN_ID=attempt_1",
            "--CCM_BATCH_INGESTION_BUCKET_NAME=mocktest",
            "--AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME=agent_adherence",
            "--REGION=mocktest",
            "--ACCOUNT_ID=mocktest",
            "--ERROR_QUEUE=mocktest",
            "--RDS_DATABASE_NAME=test-1",
            "--RDS_SECRET_MANAGE=test-2",
            "--AAD_SECRET_MANAGE=test-2",
            "--AAD_TENENT_ID=test-2",
            "--AAD_SCOPE=test-2",
            "--AAD_ENDPOINT=test-2",
        ]


class TestGetJobAttempt(unittest.TestCase, MockEnv):
    def test_get_job_attempt_adherence_with_attempt_in_id(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_job_attempt_adherence,
        )

        job_id = "my_job_id_attempt_1"
        expected_result = "1"
        result = get_job_attempt_adherence(job_id)

        assert result == expected_result

    def test_get_job_attempt_adherence_without_attempt_in_id(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_job_attempt_adherence,
        )

        job_id = "my_job_id"
        expected_result = "0"
        result = get_job_attempt_adherence(job_id)

        assert result == expected_result


class TestSendErrorToQueue(unittest.TestCase, MockEnv):
    @patch("boto3.client")
    def test_send_error_to_queue_adherence(self, mock_sqs_client):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            send_error_to_queue_adherence,
        )

        client_instance = MagicMock()
        client_instance.send_message.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200}
        }
        mock_sqs_client.return_value = client_instance
        ex = Exception("Test Exception")

        response = send_error_to_queue_adherence(ex)
        self.assertEqual(response, {"ResponseMetadata": {"HTTPStatusCode": 200}})
        client_instance.send_message.assert_called_with(
            QueueUrl="https://sqs.mocktest.amazonaws.com/mocktest/mocktest",
            DelaySeconds=5,
            MessageAttributes={
                "Title": {
                    "DataType": "String",
                    "StringValue": "Error From Glue Job agent-adherence-converter-data-job",
                },
                "Author": {"DataType": "String", "StringValue": "Glue Job"},
                "Date": {
                    "DataType": "String",
                    "StringValue": datetime.datetime.now().strftime("%m/%d/%Y"),
                },
            },
            MessageBody=str(ex),
        )


class TestGetSourceDFY(unittest.TestCase, MockEnv):
    def test_get_source_dyf_adherence(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_source_dyf_adherence,
        )

        glueContext = MagicMock()
        glueContext.create_dynamic_frame.from_catalog.return_value = "SOURCE_DYF"

        response = get_source_dyf_adherence(glueContext)

        glueContext.create_dynamic_frame.from_catalog.assert_called_once_with(
            database="ccm_agent_adherence_raw_db",
            table_name="agent_adherence",
            transformation_ctx="source_df",
            additional_options={"disableUrlUpdate": "true", "zeroDateTimeBehavior": "convertToNull"}
        )

        assert response == "SOURCE_DYF"

class TestGetCCMDbAgentDyf(unittest.TestCase, MockEnv):
    def test_get_ccm_db_agent_dyf_adherence(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_ccm_db_agent_dyf_adherence,
        )

        glueContext = MagicMock()
        glueContext.create_dynamic_frame.from_catalog.return_value = "SOURCE_DYF"

        response = get_ccm_db_agent_dyf_adherence(glueContext)

        glueContext.create_dynamic_frame.from_catalog.assert_called_once_with(
            database="ccm_rds_ccmdatastorev2",
            table_name="ccmdatastorev2_agent",
            additional_options={"disableUrlUpdate": "true", "zeroDateTimeBehavior": "convertToNull"}
        )

        assert response == "SOURCE_DYF"

class TestConvertDateStringToTimestampAdherence(unittest.TestCase, MockEnv):
    def test_take_none_value(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import convert_date_string_to_timestamp_adherence
        assert convert_date_string_to_timestamp_adherence(None, "%m%d%y") == None

    def test_convert_from_datetime(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import convert_date_string_to_timestamp_adherence
        newdatetime = convert_date_string_to_timestamp_adherence("41123", "%m%d%y")
        assert newdatetime.strftime("%Y-%m-%d") == "2023-04-11"

class TestMain(unittest.TestCase, MockEnv):
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_data_collections_and_execute_db_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_new_agent_data_collections_and_execute_db_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.process_agent_data_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_agents_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_token_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_aad_secret_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_ccm_db_agent_dyf_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_source_dyf_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_logger_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.MySQLdb.connect"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_secret_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.setup_context_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_data_collections_and_execute_db_adherence_transactions"
    )
    def test_main_with_source_does_not_have_data(
        self,
        mock_setup_context_adherence,
        mock_get_secret_adherence,
        mysql_connect,
        mock_get_logger_adherence,
        mock_get_source_dyf_adherence,
        mock_get_ccm_db_agent_dyf_adherence,
        mock_get_aad_secret_adherence,
        mock_get_token_for_adherence,
        mock_get_agents_for_adherence,
        mock_process_agent_data_for_adherence,
        mock_read_new_agent_data_collections_and_execute_db_for_adherence,
        read_data_collections_and_execute_db_adherence,
        read_data_collections_and_execute_db_adherence_transactions
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            main,
        )

        # Arrange
        mock_get_source_dyf_adherence.return_value = mock_source_dyf = Mock()
        mock_source_dyf.toDF.return_value = mock_source_df = Mock()
        mock_source_dyf.toDF.withColumn.return_value = Mock()
        mock_source_dyf.toDF.createOrReplaceTempView = Mock()
        mock_source_dyf.count.return_value = 0
        mock_source_df.head.return_value = []
        mock_source_df.collect.return_value = []

        mock_get_ccm_db_agent_dyf_adherence.return_value = mock_ccm_db_agent_dyf = Mock()
        mock_ccm_db_agent_dyf.toDF.return_value = mock_ccm_db_agent_dyf = Mock()
        mock_ccm_db_agent_dyf.head.return_value = []
        mock_ccm_db_agent_dyf.collect.return_value = [1, 2, 3]

        glueContext, spark, job = MagicMock(), MagicMock(), MagicMock()
        mock_jdbc_username, mock_jdbc_password, mock_jdbc_url = (
            MagicMock(),
            MagicMock(),
            MagicMock(),
        )
        mock_aad_clientid, mock_aad_client_secret = (
            MagicMock(),
            MagicMock(),            
        )
        mock_logger = MagicMock()
        mock_setup_context_adherence.return_value = (glueContext, spark, job)
        mock_get_secret_adherence.return_value = (
            mock_jdbc_username,
            mock_jdbc_password,
            mock_jdbc_url,
        )
        mock_get_aad_secret_adherence.return_value = (
            mock_aad_clientid,
            mock_aad_client_secret,            
        )

        mock_token = MagicMock()
        mock_get_token_for_adherence.return_value = (
           mock_token         
        )
        mock_get_logger_adherence.return_value = mock_logger
        mock_rollback = MagicMock()
        mysql_connect.rollback.return_value = mock_rollback
        spark.sql.return_value = MagicMock()
        spark.sql.Column.return_value = MagicMock()
        mock_get_agents_for_adherence.return_value = {}
        mock_process_agent_data_for_adherence.return_value = {}
        mock_read_new_agent_data_collections_and_execute_db_for_adherence.return_value = {}
        read_data_collections_and_execute_db_adherence.return_value = {}
        read_data_collections_and_execute_db_adherence_transactions.return_value = {}

        # Act
        response = main()

        assert response is None
        mock_get_source_dyf_adherence.assert_called_once()
        #mock_source_dyf.toDF.assert_called_once()
        # mock_source_df.head.assert_called()
        # mock_source_df.collect.assert_called()
        mock_logger.info.assert_called_once_with(
            "No data to process"
        )

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_data_collections_and_execute_db_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_new_agent_data_collections_and_execute_db_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.process_agent_data_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_agents_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_token_for_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_aad_secret_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_ccm_db_agent_dyf_adherence"
    )    
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_source_dyf_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_logger_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.MySQLdb.connect"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.get_secret_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.setup_context_adherence"
    )
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.read_data_collections_and_execute_db_adherence_transactions"
    )
    def test_main_with_data(
        self,
        mock_setup_context_adherence,
        mock_get_secret_adherence,
        mysql_connect,
        mock_get_logger_adherence,
        mock_get_source_dyf_adherence,
        mock_get_ccm_db_agent_dyf_adherence,
        mock_get_aad_secret_adherence,
        mock_get_token_for_adherence,
        mock_get_agents_for_adherence,
        mock_process_agent_data_for_adherence,
        mock_read_new_agent_data_collections_and_execute_db_for_adherence,        
        read_data_collections_and_execute_db_adherence,
        read_data_collections_and_execute_db_adherence_transactions
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            main,
        )

        # Arrange
        mock_get_source_dyf_adherence.return_value = mock_source_dyf = Mock()
        mock_source_dyf.toDF.return_value = mock_source_df = Mock()
        mock_source_dyf.toDF.withColumn.return_value = Mock()
        mock_source_dyf.toDF.createOrReplaceTempView = Mock()
        mock_source_dyf.count.return_value = 1
        mock_source_df.head.return_value = [{'date': '051722', 'muid': '7200', 'agentname': 'Shimizu', 'externalid': 'AC230034', 'logonid': '8184', 'department': 'test', 'customer service': 'test'}]
        mock_source_df.collect.return_value = [{'date': '051722', 'muid': '7200', 'agentname': 'Shimizu', 'externalid': 'AC230034', 'logonid': '8184', 'department': 'test', 'customer service': 'test'}]

        mock_get_ccm_db_agent_dyf_adherence.return_value = mock_ccm_db_agent_dyf = Mock()
        mock_ccm_db_agent_dyf.toDF.return_value = mock_ccm_db_agent_dyf = Mock()
        mock_ccm_db_agent_dyf.head.return_value = [1]
        mock_ccm_db_agent_dyf.collect.return_value = [1, 2, 3]

        mock_glue_context, mock_spark, mock_job = MagicMock(), MagicMock(), MagicMock()
        mock_jdbc_username, mock_jdbc_password, mock_jdbc_url = (
            MagicMock(),
            MagicMock(),
            MagicMock(),
        )
        mock_aad_clientid, mock_aad_client_secret = (
            MagicMock(),
            MagicMock(),            
        )        
        mock_logger = MagicMock()
        mock_setup_context_adherence.return_value = (mock_glue_context, mock_spark, mock_job)
        mock_get_secret_adherence.return_value = (
            mock_jdbc_username,
            mock_jdbc_password,
            mock_jdbc_url,
        )
        mock_get_aad_secret_adherence.return_value = (
            mock_aad_clientid,
            mock_aad_client_secret,            
        )

        mock_token = MagicMock()
        mock_get_token_for_adherence.return_value = (
           mock_token         
        )
        mock_get_logger_adherence.return_value = mock_logger
        mock_rollback = MagicMock()
        mysql_connect.rollback.return_value = mock_rollback
        mock_spark.sql.return_value = MagicMock()
        mock_spark.sql.Column.return_value = MagicMock()
        mock_get_agents_for_adherence.return_value = {}
        mock_process_agent_data_for_adherence.return_value = {}
        mock_read_new_agent_data_collections_and_execute_db_for_adherence.return_value = {}
        read_data_collections_and_execute_db_adherence.return_value = {}
        read_data_collections_and_execute_db_adherence_transactions.return_value = {}

        # Act
        response = main()

        assert response is None
        mock_get_source_dyf_adherence.assert_called_once()
        #mock_source_dyf.toDF.assert_called_once()
        mock_source_df.head.assert_called()
        mock_source_df.collect.assert_called()


class TestGetLoggingLevel(unittest.TestCase, MockEnv):
    @patch("os.environ.get")
    def test_get_logging_level_adherence(self, mock_env):
        import logging
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_logging_level_adherence,
        )

        mock_env.return_value = "INFO"
        assert get_logging_level_adherence() == logging.getLevelName("INFO")


class TestGetLogger(unittest.TestCase, MockEnv):
    @patch("logging.getLogger")
    @patch("logging.Formatter")
    def test_get_logging(
        self,
        mock_formatter,
        mock_get_logger_adherence,
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            get_logger_adherence,
        )

        logger = MagicMock()
        mock_get_logger_adherence.return_value = logger
        mock_formatter.return_value = MagicMock()
        assert get_logger_adherence() == logger


class TestSetupContext(unittest.TestCase, MockEnv):
    def test_setup_context_adherence_success(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            setup_context_adherence,
        )

        response = setup_context_adherence()
        assert response is not None

class TestReadDataCollectionsAndExecuteDb(unittest.TestCase, MockEnv):    
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.convert_date_string_to_timestamp_adherence"
    )
    def test_read_data_collections_and_execute_db_adherence_success(
        self,        
        mock_convert_date_string_to_timestamp_adherence
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            read_data_collections_and_execute_db_adherence,
        )
        mock_conn = MagicMock()
        records = [MagicMock(), MagicMock()]
        logger = MagicMock()
        mock_conn.cursor.return_value = MagicMock()
        mock_conn.cursor.executemany.return_value = MagicMock()        
        mock_convert_date_string_to_timestamp_adherence.return_value = MagicMock()
        logger.info.return_value = MagicMock()
        read_data_collections_and_execute_db_adherence(mock_conn, records, logger)
        assert True

    def test_read_data_collections_and_execute_db_adherence_fail(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            read_data_collections_and_execute_db_adherence,
        )
        assert True

class TestGetSecretAdherence(unittest.TestCase, MockEnv):
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_success(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.return_value = {'SecretString': '{"username":"username","password":"password","engine":"mysql","host":"mochost","port":33067,"dbClusterIdentifier":"mockcluster","hostProxyWR":"mockwr","hostProxyRD":"mockrd"}'}
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        response = get_secret_adherence()
        print(response)
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_fail_decryption(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'DecryptionFailureException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_fail_internal(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InternalServiceErrorException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_fail_invalid_parameter(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InvalidParameterException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_fail_invalid_request(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InvalidRequestException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_secret_adherence_fail_resource_not_found(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'ResourceNotFoundException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_secret_adherence()
        assert True

class TestAadGetSecretAdherence(unittest.TestCase, MockEnv):
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_success(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.return_value = {'SecretString': '{"clientId":"mockclientid","clientSecret":"mockclientsecret"}'}
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        response = get_aad_secret_adherence()
        print(response)
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_fail_decryption(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'DecryptionFailureException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_aad_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_fail_internal(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InternalServiceErrorException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_aad_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_fail_invalid_parameter(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InvalidParameterException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_aad_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_fail_invalid_request(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'InvalidRequestException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_aad_secret_adherence()
        assert True

    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.boto3.session.Session"
    )
    def test_get_aad_secret_adherence_fail_resource_not_found(self, mock_session_class):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import get_aad_secret_adherence
        mock_session_object = Mock()
        mock_client = Mock()
        mock_client.get_secret_value.side_effect  = ClientError({'Error': {'Code': 'ResourceNotFoundException'}}, 'operation_name')
        mock_session_object.client.return_value = mock_client
        mock_session_class.return_value = mock_session_object
        with pytest.raises(ClientError):
            get_aad_secret_adherence()
        assert True

class TestProcessAgentDataForAdherence(unittest.TestCase, MockEnv):
    @patch(
        "stacks.agent_adherence_converter.scripts.agent_adherence_data_job.uuid.uuid4"
    )
    def test_process_agent_data_for_adherence_success(
        self,
        mock_uuid4
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            process_agent_data_for_adherence,
        )
        data = [MagicMock(), MagicMock()]
        new_agent_ids_list = [MagicMock(), MagicMock()]
        mock_uuid4.return_value = MagicMock()
        response = process_agent_data_for_adherence(data, new_agent_ids_list)
        print(response)
        assert True

    def test_process_agent_data_for_adherence_fail(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            process_agent_data_for_adherence,
        )
        assert True

class TestReadNewAgentDataCollectionsAndExecuteDbForAdherence(unittest.TestCase, MockEnv):

    def test_read_new_agent_data_collections_and_execute_db_for_adherence_success(
        self
    ):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            read_new_agent_data_collections_and_execute_db_for_adherence,
        )
        mock_conn = MagicMock()
        records = [MagicMock(), MagicMock()]
        mock_conn.cursor.return_value = MagicMock()
        mock_conn.cursor.executemany.return_value = MagicMock()
        read_new_agent_data_collections_and_execute_db_for_adherence(mock_conn, records)
        assert True

    def test_read_new_agent_data_collections_and_execute_db_for_adherence_fail(self):
        from stacks.agent_adherence_converter.scripts.agent_adherence_data_job import (
            read_new_agent_data_collections_and_execute_db_for_adherence,
        )
        assert True

if __name__ == "__main__":
    unittest.main()
