service: ${self:custom.service}-agent-schedule-converter-dlq
variablesResolutionMode: 20210326
params: ${file(../../../defaults.yml):custom.params}

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}  
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  params: ${self:custom.defaults.custom.params.${self:provider.stage}}
  sns:
    base:
      arn: arn:aws:sns:${self:provider.region}:${aws:accountId}
    topics:
      GlueJobAlarmTopic:
        name: ${self:service}-alarm-topic-${self:provider.stage}
        arn: ${self:custom.sns.base.arn}:${self:custom.sns.topics.GlueJobAlarmTopic.name}
  sqs:
    base:
      name: ${self:service}
      arn: arn:aws:sqs:${self:provider.region}:${aws:accountId}       
    GlueJobFailedQueue:
      name: ${self:custom.sqs.base.name}-failed-queue-${self:provider.stage}
      arn: ${self:custom.sqs.base.arn}:${self:custom.sqs.GlueJobFailedQueue.name}
      url: https://sqs.${self:provider.region}.amazonaws.com/${aws:accountId}/${self:custom.sqs.GlueJobFailedQueue.name}

resources:
  Resources:
    GlueJobFailedQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName : ${self:custom.sqs.GlueJobFailedQueue.name}   
    GlueJobAlarmSNSTopic:
      Type: AWS::SNS::Topic
      Properties:
        DisplayName: ${self:custom.sns.topics.GlueJobAlarmTopic.name}
        Subscription:
          - Endpoint: ${param:AGENT_SCHEDULE_DLQ_EMAIL} #change email to appropriate alias
            Protocol: "email"

    GlueJobErrorAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmDescription: "Alarm if queue depth grows beyond or equal to 1 messages"
        Namespace: "AWS/SQS"
        MetricName: NumberOfMessagesSent
        Dimensions:
          - Name: QueueName
            Value : ${self:custom.sqs.GlueJobFailedQueue.name}
        Statistic: Sum
        Period: 300
        EvaluationPeriods: 1
        Threshold: 1
        ComparisonOperator: GreaterThanOrEqualToThreshold
        AlarmActions:
          - Ref: GlueJobAlarmSNSTopic
        InsufficientDataActions:
          - Ref: GlueJobAlarmSNSTopic
        TreatMissingData: notBreaching

  Outputs:
    GlueJobDLQName:
      Description: Agent Adherence Converter Data Job Failed SQS Name
      Value: ${self:custom.sqs.GlueJobFailedQueue.name}
    GlueJobDLQUrl:
      Description: Agent Adherence Converter Data Job Failed SQS URL
      Value: ${self:custom.sqs.GlueJobFailedQueue.url}
    GlueJobDLQArn:
      Description: Agent Adherence Converter Data Job Failed SQS Arn
      Value: ${self:custom.sqs.GlueJobFailedQueue.arn}