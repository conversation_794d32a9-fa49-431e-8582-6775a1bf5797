service: ${self:custom.service}-aa-glue-catalog

provider:
  name: aws
  runtime: python3.11
  architecture: arm64
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  defaultParams: ${self:custom.defaults.custom.params}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}

resources:
  Resources:
    Database: ${file(./resources/glueDatabase.yml)}
    SourceTable: ${file(./resources/glueSourceTable.yml)}
