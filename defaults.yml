service: ac-odh-batch-ingestion-ccm

custom:
  tags:
    project: Air Canada Argo ODH
    department: Digital
    service: ac-odh-batch-ingestion-ccm
    repository: https://bitbucket.org/aircanada-m3/ac-odh-batch-ingestion-ccm
    

    tower: Operation
    department-id: 1904
    department-name: ops-digital
    CostCode: 1904
    ProjectName: CCM
    SharedResource: NO
    Application: ODH Batch Ingestion CCM
    TechOwner: 'Prade<PERSON> Nishantha'
    BusinessOwner: 'Pradeep Nishantha'
    Environment: ${self:provider.stage}
    Criticality: MAJOR
    Sensitivity: Medium
    RecoveryTimeObjective: 15
    RecoveryPointObjective: 15
    Type: Customer
    BusinessImpact: High
    ComplianceRequirement: N/A
    Observability: YES

  params:
    intca1:
      CCM_BATCH_INGESTION_BUCKET_NAME: ac-odh-raw-batch-storage-${sls:stage}
      AGENT_SCHEDULE_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/raw
      AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/formatted
      AGENT_ADHERENCE_DLQ_EMAIL: <EMAIL>      
      AGENT_ADHERENCE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/formatted
      AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/raw
      RDS_CONNCET_VPC_ID: vpc-0b8bcf38152af77cc
      RDS_CONNECTION_SUBNET_ID_A: subnet-07612cb342a5be91d
      RDS_CONNECTION_SUBNET_ID_B: subnet-01644fc5d5a72b235
      RDS_CONNECTION_SUBNET_ID_D: subnet-0656557afe0f2bc5e
      DEFAULT_LANG: en
      LAYER_STACK_NAME: dbaas-sre-infra-v3-layer-${sls:stage}
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-${sls:stage}-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: ${sls:stage}
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      LOG_STREAM_NAME: SLSInfo
      DEBUG_MODE: true      
      LAMBDA_TIMEOUT: 300
      TRIGGER_MEMORY_SIZE: 512
      STATUS_EVENT: enable
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 1
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-intca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-intca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      STORE_DB_ENV: int
      CW_RETENTION_IN_DAY: 7
      CW_PERMISSION: Allow
      RDS_SECRET_MANAGE: ccm/db-credentials
      RDS_DATABASE_NAME: CcmDataStoreV2
      AAD_SECRET_MANAGE: /intca1/azure-active-directory/api-credentials
      AAD_TENENT_ID: '491d83df-1091-40f8-bcf9-b112f9a35fcf'
      AAD_SCOPE: 'https://graph.microsoft.com/.default'
      AAD_ENDPOINT: 'https://graph.microsoft.com/v1.0/users'
      AGENT_ADHERENCE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *
      AGENT_SCHEDULE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *

    batca1:
      CCM_BATCH_INGESTION_BUCKET_NAME: ac-odh-raw-batch-storage-${sls:stage}
      AGENT_SCHEDULE_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/raw
      AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/formatted
      AGENT_ADHERENCE_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/formatted
      AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/raw
      RDS_CONNCET_VPC_ID: vpc-0abed1f5dc9bec60b
      RDS_CONNECTION_SUBNET_ID_A: subnet-093ec232ccaaf9236
      RDS_CONNECTION_SUBNET_ID_B: subnet-0e040f56cf4be65bc
      RDS_CONNECTION_SUBNET_ID_D: subnet-0fd7784cf1d74d946
      DEFAULT_LANG: en
      LAYER_STACK_NAME: dbaas-sre-infra-v3-layer-${sls:stage}
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-${sls:stage}-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: ${sls:stage}
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      LOG_STREAM_NAME: SLSInfo
      DEBUG_MODE: true
      LAMBDA_TIMEOUT: 300
      TRIGGER_MEMORY_SIZE: 512
      STATUS_EVENT: enable
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 1
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-batca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-batca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      STORE_DB_ENV: bat
      CW_RETENTION_IN_DAY: 7
      CW_PERMISSION: Allow
      RDS_SECRET_MANAGE: ccm/db-credentials
      RDS_DATABASE_NAME: CcmDataStoreV2
      AAD_SECRET_MANAGE: /batca1/azure-active-directory/api-credentials
      AAD_TENENT_ID: '491d83df-1091-40f8-bcf9-b112f9a35fcf'
      AAD_SCOPE: 'https://graph.microsoft.com/.default'
      AAD_ENDPOINT: 'https://graph.microsoft.com/v1.0/users'
      AGENT_ADHERENCE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *
      AGENT_SCHEDULE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *

    crtca1:
      CCM_BATCH_INGESTION_BUCKET_NAME: ac-odh-raw-batch-storage-${sls:stage}
      AGENT_SCHEDULE_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/raw
      AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/formatted
      AGENT_ADHERENCE_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/formatted
      AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/raw
      RDS_CONNCET_VPC_ID: vpc-05c9f9824109cd1e7
      RDS_CONNECTION_SUBNET_ID_A: subnet-0e030f7de3818424f
      RDS_CONNECTION_SUBNET_ID_B: subnet-0f064c984d71076c7
      RDS_CONNECTION_SUBNET_ID_D: subnet-0cfe753e530b47cc8
      DEFAULT_LANG: en
      LAYER_STACK_NAME: dbaas-sre-infra-v3-layer-${sls:stage}
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-${sls:stage}-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: ${sls:stage}
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      LOG_STREAM_NAME: SLSInfo
      DEBUG_MODE: true
      LAMBDA_TIMEOUT: 300
      TRIGGER_MEMORY_SIZE: 512
      STATUS_EVENT: enable
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 1
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-crtca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-crtca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      STORE_DB_ENV: crt
      CW_RETENTION_IN_DAY: 7
      CW_PERMISSION: Deny
      RDS_SECRET_MANAGE: ccm/db-credentials
      RDS_DATABASE_NAME: CcmDataStoreV2
      AAD_SECRET_MANAGE: /crtca1/azure-active-directory/api-credentials
      AAD_TENENT_ID: '491d83df-1091-40f8-bcf9-b112f9a35fcf'
      AAD_SCOPE: 'https://graph.microsoft.com/.default'
      AAD_ENDPOINT: 'https://graph.microsoft.com/v1.0/users'
      AGENT_ADHERENCE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *
      AGENT_SCHEDULE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *

    preprodca1:
      CCM_BATCH_INGESTION_BUCKET_NAME: ac-odh-raw-batch-storage-${sls:stage}
      AGENT_SCHEDULE_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/raw
      AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/formatted
      AGENT_ADHERENCE_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/formatted
      AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/raw
      RDS_CONNCET_VPC_ID: vpc-0019f2acd966dc61c
      RDS_CONNECTION_SUBNET_ID_A: subnet-0e01bb22ef83c9ca3
      RDS_CONNECTION_SUBNET_ID_B: subnet-06dd18a1cfb259360
      RDS_CONNECTION_SUBNET_ID_D: subnet-0ce0a9dbac4cfc597
      DEFAULT_LANG: en
      LAYER_STACK_NAME: dbaas-sre-infra-v3-layer-${sls:stage}
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-${sls:stage}-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: ${sls:stage}
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      LOG_STREAM_NAME: SLSInfo
      DEBUG_MODE: true
      LAMBDA_TIMEOUT: 300
      TRIGGER_MEMORY_SIZE: 512
      STATUS_EVENT: enable
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 1
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-preprodca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-preprodca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      STORE_DB_ENV: preprod
      CW_RETENTION_IN_DAY: 7
      CW_PERMISSION: Allow
      RDS_SECRET_MANAGE: ccm/db-credentials
      RDS_DATABASE_NAME: CcmDataStoreV2
      #AAD_SECRET_MANAGE:
      AAD_TENENT_ID: '491d83df-1091-40f8-bcf9-b112f9a35fcf'
      AAD_SCOPE: 'https://graph.microsoft.com/.default'
      AAD_ENDPOINT: 'https://graph.microsoft.com/v1.0/users'
      AGENT_ADHERENCE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *
      AGENT_SCHEDULE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *


    prodca1:
      CCM_BATCH_INGESTION_BUCKET_NAME: ac-odh-raw-batch-storage-${sls:stage}
      AGENT_SCHEDULE_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_SCHEDULE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/raw
      AGENT_SCHEDULE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_schedule/formatted
      AGENT_ADHERENCE_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_TEXT_TO_CSV_DLQ_EMAIL: <EMAIL>
      AGENT_ADHERENCE_FORMATTED_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/formatted
      AGENT_ADHERENCE_RAW_INGESTION_BUCKET_PREFIX_NAME: data-ingestion/ccm/agent_adherence/raw
      RDS_CONNCET_VPC_ID: vpc-0019f2acd966dc61c
      RDS_CONNECTION_SUBNET_ID_A: subnet-08d31a877e065ace8
      RDS_CONNECTION_SUBNET_ID_B: subnet-090f8cb4fe8356b6d
      RDS_CONNECTION_SUBNET_ID_D: subnet-06d5c348a9e511629
      DEFAULT_LANG: en
      LAYER_STACK_NAME: dbaas-sre-infra-v3-layer-${sls:stage}
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-${sls:stage}-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: ${sls:stage}
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      LOG_STREAM_NAME: SLSInfo
      DEBUG_MODE: true
      LAMBDA_TIMEOUT: 300
      TRIGGER_MEMORY_SIZE: 512
      STATUS_EVENT: enable
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 1
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-prodca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-prodca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      STORE_DB_ENV: prod
      CW_RETENTION_IN_DAY: 7
      CW_PERMISSION: Allow
      RDS_SECRET_MANAGE: ccm/db-credentials
      RDS_DATABASE_NAME: CcmDataStoreV2
      AAD_SECRET_MANAGE: /prodca1/azure-active-directory/api-credentials
      AAD_TENENT_ID: '491d83df-1091-40f8-bcf9-b112f9a35fcf'
      AAD_SCOPE: 'https://graph.microsoft.com/.default'
      AAD_ENDPOINT: 'https://graph.microsoft.com/v1.0/users'
      AGENT_ADHERENCE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *
      AGENT_SCHEDULE_BATCH_INGESTION_TRIGGER_SCHEDULE: 0/15 * * * ? *