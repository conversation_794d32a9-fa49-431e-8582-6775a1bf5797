# #! /bin/bash

# # Assume Role - https://aws.amazon.com/premiumsupport/knowledge-center/iam-assume-role-cli/

# stage=$1
# region=$2
# deploy_path=$3
# container_name=$4

# if [[ -z $stage ]];
# then
#     echo "missing stage arg"
#     exit 1
# fi

# if [[ -z $region ]];
# then
#     echo "missing region arg"
#     exit 1
# fi

# if [[ -z $deploy_path ]];
# then
#     echo "missing deployment folder path"
#     exit 1
# fi

# if [[ -z $container_name ]];
# then
#     echo "missing container name"
#     exit 1
# fi

# # Source .env if local
# #source .env
# filename='.env'
# if [[ -f $filename ]];
# then
# 	source $filename
# else
# 	echo "Env File does not exist. Nothing to do. Sucess!!"
# fi

# if [[ $stage == 'intca1' ]];
# then
#     CICD_ROLE_ARN=$DBAAS_INT_CICD_ROLE_ARN
#     CICD_USER_AWS_ACCESS_KEY=$DBAAS_INT_CICD_USER_AWS_ACCESS_KEY
#     CICD_USER_AWS_SECRET_ACCESS_KEY=$DBAAS_INT_CICD_USER_AWS_SECRET_ACCESS_KEY
# elif [[ $stage == 'batca1' ]];
# then
#     CICD_ROLE_ARN=$DBAAS_BAT_CICD_ROLE_ARN
#     CICD_USER_AWS_ACCESS_KEY=$DBAAS_BAT_CICD_USER_AWS_ACCESS_KEY
#     CICD_USER_AWS_SECRET_ACCESS_KEY=$DBAAS_BAT_CICD_USER_AWS_SECRET_ACCESS_KEY
# elif [[ $stage == 'crtca1' ]];
# then
#     CICD_ROLE_ARN=$DBAAS_CRT_CICD_ROLE_ARN
#     CICD_USER_AWS_ACCESS_KEY=$DBAAS_CRT_CICD_USER_AWS_ACCESS_KEY
#     CICD_USER_AWS_SECRET_ACCESS_KEY=$DBAAS_CRT_CICD_USER_AWS_SECRET_ACCESS_KEY
# elif [[ $stage == 'preprodca1' ]];
# then
#    CICD_ROLE_ARN=$DBAAS_PREPROD_CICD_ROLE_ARN
#    CICD_USER_AWS_ACCESS_KEY=$DBAAS_PREPROD_CICD_USER_AWS_ACCESS_KEY
#    CICD_USER_AWS_SECRET_ACCESS_KEY=$DBAAS_PREPROD_CICD_USER_AWS_SECRET_ACCESS_KEY
# elif [[ $stage == 'prodca1' ]];
# then
#    CICD_ROLE_ARN=$DBAAS_PROD_CICD_ROLE_ARN
#    CICD_USER_AWS_ACCESS_KEY=$DBAAS_PROD_CICD_USER_AWS_ACCESS_KEY
#    CICD_USER_AWS_SECRET_ACCESS_KEY=$DBAAS_PROD_CICD_USER_AWS_SECRET_ACCESS_KEY
# fi

# AWS_ACCESS_KEY_ID=$CICD_USER_AWS_ACCESS_KEY
# AWS_SECRET_ACCESS_KEY=$CICD_USER_AWS_SECRET_ACCESS_KEY
# AWSCLISession=${BITBUCKET_REPO_SLUG}-BitBucket

# assume_call=$(aws sts assume-role --role-arn $CICD_ROLE_ARN --role-session-name ${AWSCLISession} )


# assumed_aws_access_key=$(echo $assume_call | jq -r .Credentials.AccessKeyId )
# assumed_aws_access_secret_key=$(echo $assume_call | jq -r .Credentials.SecretAccessKey)
# assumed_aws_session_token=$(echo $assume_call | jq -r .Credentials.SessionToken)

# AWS_ACCESS_KEY_ID=$assumed_aws_access_key
# AWS_SECRET_ACCESS_KEY=$assumed_aws_access_secret_key
# AWS_SESSION_TOKEN=$assumed_aws_session_token

# # make file
# rm -rf $BITBUCKET_CLONE_DIR/.aws
# mkdir $BITBUCKET_CLONE_DIR/.aws
# touch $BITBUCKET_CLONE_DIR/.aws/config
# touch $BITBUCKET_CLONE_DIR/.aws/credentials

# # push config
# echo -e "[default] \nregion = $AWS_REGION \n" >> $BITBUCKET_CLONE_DIR/.aws/config

# echo -e "[default] \naws_access_key_id = $AWS_ACCESS_KEY_ID \naws_secret_access_key = $AWS_SECRET_ACCESS_KEY \naws_session_token = $AWS_SESSION_TOKEN" >> $BITBUCKET_CLONE_DIR/.aws/credentials

# # pull docker image.
# docker pull amazon/aws-glue-libs:glue_libs_3.0.0_image_01

# docker run -v $BITBUCKET_CLONE_DIR/.aws:/home/<USER>/.aws -v $BITBUCKET_CLONE_DIR/$deploy_path:/home/<USER>/workspace/ -p 4040:4040 -p 18080:18080 --name $container_name amazon/aws-glue-libs:glue_libs_3.0.0_image_01 -c "python3 -m pip install --upgrade pip && python3 -m venv venv && source ./venv/bin/activate && pip install -r requirements.txt && python3 -m pytest --cov-report xml --cov"

#! /bin/bash

container_name=test-container

if [[ -z $container_name ]];
then
    echo "missing container name"
    exit 1
fi
GITHUB_WORKSPACE=/home/<USER>/work/ac-odh-batch-ingestion-ccm/ac-odh-batch-ingestion-ccm
if [[ -z $GITHUB_WORKSPACE ]];
then
    echo "missing GITHUB_WORKSPACE"
    exit 1
fi

docker run --rm -v "${GITHUB_WORKSPACE}:/home/<USER>/workspace" \
    --name $container_name \
    amazon/aws-glue-libs:glue_libs_4.0.0_image_01 \
    -c "python3 -m pip install --upgrade pip && python3 -m venv venv && source ./venv/bin/activate && python -m pip install -r requirements.txt && python3 -m pytest --cov-report xml --cov"
